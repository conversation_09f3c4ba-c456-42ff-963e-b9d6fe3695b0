import React, { useState } from 'react';
import {
  Factory,
  Calendar,
  ClipboardList,
  TrendingUp,
  Play,
  Plus,
  Search,
  Filter,
  Download,
  Edit,
  Eye,
  Trash2,
  Clock,
  CheckCircle,
  AlertTriangle,
  Users,
  Package
} from 'lucide-react';

const ProductionDepartment = () => {
  const [activeTab, setActiveTab] = useState<'plans' | 'orders' | 'progress' | 'reports'>('plans');

  // Dữ liệu mẫu cho KẾ HOẠCH SẢN XUẤT
  const planData = [
    {
      id: 1,
      maKeHoach: 'KH-SX001/2024',
      tenKeHoach: 'Kế hoạch sản xuất tôm đông lạnh tháng 3/2024',
      sanPham: 'Tôm đông lạnh size 20-30',
      soLuongKeHoach: 50000,
      donVi: 'kg',
      ngayBatDau: '2024-03-01',
      ngayKetThuc: '2024-03-31',
      trangThai: '<PERSON><PERSON> thực hiện',
      tiLeTienDo: 75,
      soLuongDaLam: 37500,
      soLuongCon<PERSON>: 12500,
      nguoi<PERSON>huTrach: '<PERSON><PERSON><PERSON><PERSON>',
      phong<PERSON><PERSON>: 'Ph<PERSON> xưởng chế biến',
      g<PERSON><PERSON><PERSON>: 'Ưu tiên đơn hàng xuất khẩu'
    },
    {
      id: 2,
      maKeHoach: 'KH-SX002/2024',
      tenKeHoach: 'Kế hoạch sản xuất cá tra phi lê tháng 3/2024',
      sanPham: 'Cá tra phi lê đông lạnh',
      soLuongKeHoach: 30000,
      donVi: 'kg',
      ngayBatDau: '2024-03-05',
      ngayKetThuc: '2024-03-25',
      trangThai: 'Hoàn thành',
      tiLeTienDo: 100,
      soLuongDaLam: 30000,
      soLuongConLai: 0,
      nguoiPhuTrach: 'Trần Thị Bình',
      phongBan: 'Phân xưởng chế biến',
      ghiChu: 'Hoàn thành trước hạn 3 ngày'
    }
  ];

  // Dữ liệu mẫu cho LỆNH SẢN XUẤT
  const orderData = [
    {
      id: 1,
      maLenh: 'LSX-001/2024',
      maDonHang: 'DH-XK2024015',
      sanPham: 'Tôm sú đông lạnh size 15-20',
      soLuong: 15000,
      donVi: 'kg',
      ngayBatDau: '2024-03-20',
      ngayKetThuc: '2024-03-22',
      trangThai: 'Đang sản xuất',
      ca: 'Ca 1',
      mayMoc: 'Dây chuyền 1',
      nguoiVanHanh: 'Lê Văn Cường',
      tiLeTienDo: 60,
      chatLuong: 'Đạt chuẩn',
      ghiChu: 'Kiểm tra chất lượng mỗi 2 giờ'
    },
    {
      id: 2,
      maLenh: 'LSX-002/2024',
      maDonHang: 'DH-ND2024008',
      sanPham: 'Cá basa phi lê tươi',
      soLuong: 8000,
      donVi: 'kg',
      ngayBatDau: '2024-03-18',
      ngayKetThuc: '2024-03-19',
      trangThai: 'Hoàn thành',
      ca: 'Ca 2',
      mayMoc: 'Dây chuyền 2',
      nguoiVanHanh: 'Phạm Thị Dung',
      tiLeTienDo: 100,
      chatLuong: 'Xuất sắc',
      ghiChu: 'Chất lượng vượt tiêu chuẩn'
    }
  ];

  // Dữ liệu mẫu cho THEO DÕI TIẾN ĐỘ
  const progressData = [
    {
      id: 1,
      ngaySanXuat: '2024-03-20',
      ca: 'Ca 1 (6:00-14:00)',
      dayChuyenSo: 'Dây chuyền 1',
      sanPham: 'Tôm sú đông lạnh',
      keHoach: 5000,
      thucTe: 4800,
      tiLeDatKeHoach: 96,
      chatLuong: 'Đạt',
      soLuongLoi: 50,
      tyLeLoi: 1.04,
      nguoiGiamSat: 'Nguyễn Văn An',
      ghiChu: 'Máy móc hoạt động ổn định'
    },
    {
      id: 2,
      ngaySanXuat: '2024-03-20',
      ca: 'Ca 2 (14:00-22:00)',
      dayChuyenSo: 'Dây chuyền 2',
      sanPham: 'Cá tra phi lê',
      keHoach: 4000,
      thucTe: 4200,
      tiLeDatKeHoach: 105,
      chatLuong: 'Tốt',
      soLuongLoi: 20,
      tyLeLoi: 0.48,
      nguoiGiamSat: 'Trần Thị Bình',
      ghiChu: 'Vượt kế hoạch 5%'
    }
  ];

  // Dữ liệu mẫu cho BÁO CÁO SẢN XUẤT
  const reportData = [
    {
      id: 1,
      thang: 'Tháng 2/2024',
      tongSanLuong: 180000,
      keHoachThang: 175000,
      tiLeDatKeHoach: 102.9,
      sanPhamChinh: 'Tôm đông lạnh',
      sanLuongSanPhamChinh: 120000,
      chatLuongTrungBinh: 96.5,
      tyLeLoi: 2.1,
      hieuSuatMayMoc: 94.2,
      soNhanVien: 85,
      soGioLamViec: 6800,
      ghiChu: 'Tháng sản xuất tốt, đạt kế hoạch'
    },
    {
      id: 2,
      thang: 'Tháng 1/2024',
      tongSanLuong: 165000,
      keHoachThang: 170000,
      tiLeDatKeHoach: 97.1,
      sanPhamChinh: 'Cá tra phi lê',
      sanLuongSanPhamChinh: 100000,
      chatLuongTrungBinh: 95.8,
      tyLeLoi: 2.8,
      hieuSuatMayMoc: 91.5,
      soNhanVien: 82,
      soGioLamViec: 6560,
      ghiChu: 'Chưa đạt kế hoạch do thiếu nguyên liệu'
    }
  ];

  // State for modals
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);

  const openDetailModal = (item: any) => {
    setSelectedItem(item);
    setIsDetailModalOpen(true);
  };

  const closeDetailModal = () => {
    setIsDetailModalOpen(false);
    setSelectedItem(null);
  };

  const tabs = [
    { id: 'plans', name: 'KẾ HOẠCH SẢN XUẤT', icon: <Calendar className="w-4 h-4" />, count: planData.length },
    { id: 'orders', name: 'LỆNH SẢN XUẤT', icon: <ClipboardList className="w-4 h-4" />, count: orderData.length },
    { id: 'progress', name: 'THEO DÕI TIẾN ĐỘ', icon: <TrendingUp className="w-4 h-4" />, count: progressData.length },
    { id: 'reports', name: 'BÁO CÁO SẢN XUẤT', icon: <Factory className="w-4 h-4" />, count: reportData.length }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2 flex items-center">
            <Factory className="w-8 h-8 text-blue-600 mr-3" />
            Phòng QLSX
          </h1>
          <p className="text-gray-600">Quản lý kế hoạch sản xuất, lệnh sản xuất, theo dõi tiến độ và báo cáo</p>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
          {/* Tổng quan kế hoạch */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <Calendar className="w-5 h-5 text-blue-600 mr-2" />
              Kế hoạch SX
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Tổng kế hoạch</span>
                <span className="text-lg font-bold text-blue-600">{planData.length}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Đang thực hiện</span>
                <span className="text-lg font-bold text-green-600">
                  {planData.filter(item => item.trangThai === 'Đang thực hiện').length}
                </span>
              </div>
            </div>
          </div>

          {/* Tổng quan lệnh sản xuất */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <ClipboardList className="w-5 h-5 text-green-600 mr-2" />
              Lệnh sản xuất
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Tổng lệnh</span>
                <span className="text-lg font-bold text-green-600">{orderData.length}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Đang SX</span>
                <span className="text-lg font-bold text-blue-600">
                  {orderData.filter(item => item.trangThai === 'Đang sản xuất').length}
                </span>
              </div>
            </div>
          </div>

          {/* Tổng quan tiến độ */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <TrendingUp className="w-5 h-5 text-purple-600 mr-2" />
              Tiến độ SX
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Hiệu suất TB</span>
                <span className="text-lg font-bold text-purple-600">
                  {progressData.length > 0 ? `${Math.round(progressData.reduce((acc, item) => acc + item.tiLeDatKeHoach, 0) / progressData.length)}%` : '0%'}
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Chất lượng</span>
                <span className="text-lg font-bold text-green-600">
                  {progressData.length > 0 ? `${(100 - progressData.reduce((acc, item) => acc + item.tyLeLoi, 0) / progressData.length).toFixed(1)}%` : '0%'}
                </span>
              </div>
            </div>
          </div>

          {/* Tổng quan báo cáo */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <Package className="w-5 h-5 text-orange-600 mr-2" />
              Sản lượng
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Tháng này</span>
                <span className="text-lg font-bold text-orange-600">
                  {reportData.length > 0 ? `${(reportData[0].tongSanLuong / 1000).toFixed(0)}K kg` : '0'}
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Đạt KH</span>
                <span className="text-lg font-bold text-green-600">
                  {reportData.length > 0 ? `${reportData[0].tiLeDatKeHoach.toFixed(1)}%` : '0%'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.icon}
                  {tab.name}
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    activeTab === tab.id ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                  }`}>
                    {tab.count}
                  </span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Action Bar */}
        <div className="mb-6 bg-white rounded-lg shadow-sm p-4">
          <div className="flex flex-wrap gap-4 items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Tìm kiếm..."
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-64"
                />
              </div>
              <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                <Filter className="h-4 w-4" />
                Lọc
              </button>
            </div>
            <div className="flex items-center gap-2">
              <button className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                <Download className="h-4 w-4" />
                Xuất Excel
              </button>
              <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                <Plus className="h-4 w-4" />
                Thêm mới
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="bg-white rounded-lg shadow-sm">
          {/* KẾ HOẠCH SẢN XUẤT */}
          {activeTab === 'plans' && (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã kế hoạch</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên kế hoạch</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sản phẩm</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Số lượng</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thời gian</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tiến độ</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hoạt động</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {planData.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{item.maKeHoach}</td>
                      <td className="px-4 py-4 text-sm text-gray-900 max-w-xs">{item.tenKeHoach}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.sanPham}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>
                          <div className="font-medium">{item.soLuongKeHoach.toLocaleString()} {item.donVi}</div>
                          <div className="text-xs text-gray-500">
                            Đã làm: {item.soLuongDaLam.toLocaleString()} {item.donVi}
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>
                          <div className="flex items-center text-xs text-gray-500">
                            <Calendar className="w-3 h-3 mr-1" />
                            {item.ngayBatDau}
                          </div>
                          <div className="flex items-center text-xs text-gray-500">
                            <Calendar className="w-3 h-3 mr-1" />
                            {item.ngayKetThuc}
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center">
                          <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full"
                              style={{ width: `${item.tiLeTienDo}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium">{item.tiLeTienDo}%</span>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.trangThai === 'Hoàn thành' ? 'bg-green-100 text-green-800' :
                          item.trangThai === 'Đang thực hiện' ? 'bg-blue-100 text-blue-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {item.trangThai}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => openDetailModal(item)}
                            className="text-blue-600 hover:text-blue-800"
                            title="Xem chi tiết"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-800" title="Chỉnh sửa">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="text-red-600 hover:text-red-800" title="Xóa">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* LỆNH SẢN XUẤT */}
          {activeTab === 'orders' && (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã lệnh</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã đơn hàng</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sản phẩm</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Số lượng</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ca/Máy móc</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tiến độ</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Chất lượng</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hoạt động</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {orderData.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{item.maLenh}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.maDonHang}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.sanPham}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className="font-medium">{item.soLuong.toLocaleString()} {item.donVi}</span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>
                          <div className="font-medium">{item.ca}</div>
                          <div className="text-xs text-gray-500">{item.mayMoc}</div>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center">
                          <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div
                              className="bg-green-600 h-2 rounded-full"
                              style={{ width: `${item.tiLeTienDo}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium">{item.tiLeTienDo}%</span>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.chatLuong === 'Xuất sắc' ? 'bg-green-100 text-green-800' :
                          item.chatLuong === 'Đạt chuẩn' ? 'bg-blue-100 text-blue-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {item.chatLuong}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.trangThai === 'Hoàn thành' ? 'bg-green-100 text-green-800' :
                          item.trangThai === 'Đang sản xuất' ? 'bg-blue-100 text-blue-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {item.trangThai}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => openDetailModal(item)}
                            className="text-blue-600 hover:text-blue-800"
                            title="Xem chi tiết"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-800" title="Chỉnh sửa">
                            <Edit className="w-4 h-4" />
                          </button>
                          {item.trangThai === 'Đang sản xuất' && (
                            <button className="text-orange-600 hover:text-orange-800" title="Tạm dừng">
                              <Clock className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* THEO DÕI TIẾN ĐỘ */}
          {activeTab === 'progress' && (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày SX</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ca làm việc</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dây chuyền</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sản phẩm</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">KH/Thực tế</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Đạt KH</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Chất lượng</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tỷ lệ lỗi</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hoạt động</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {progressData.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{item.ngaySanXuat}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.ca}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.dayChuyenSo}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.sanPham}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>
                          <div className="font-medium">KH: {item.keHoach.toLocaleString()}</div>
                          <div className="text-xs text-gray-500">TT: {item.thucTe.toLocaleString()}</div>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`font-bold ${item.tiLeDatKeHoach >= 100 ? 'text-green-600' : 'text-red-600'}`}>
                          {item.tiLeDatKeHoach}%
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.chatLuong === 'Tốt' ? 'bg-green-100 text-green-800' :
                          item.chatLuong === 'Đạt' ? 'bg-blue-100 text-blue-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {item.chatLuong}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`font-medium ${item.tyLeLoi <= 1 ? 'text-green-600' : 'text-red-600'}`}>
                          {item.tyLeLoi}%
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => openDetailModal(item)}
                            className="text-blue-600 hover:text-blue-800"
                            title="Xem chi tiết"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-800" title="Cập nhật">
                            <Edit className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* BÁO CÁO SẢN XUẤT */}
          {activeTab === 'reports' && (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thời gian</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tổng sản lượng</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kế hoạch</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Đạt KH</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sản phẩm chính</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Chất lượng TB</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hiệu suất</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nhân viên</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hoạt động</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {reportData.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{item.thang}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className="font-bold text-green-600">
                          {(item.tongSanLuong / 1000).toFixed(0)}K kg
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className="font-medium">
                          {(item.keHoachThang / 1000).toFixed(0)}K kg
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`font-bold ${item.tiLeDatKeHoach >= 100 ? 'text-green-600' : 'text-red-600'}`}>
                          {item.tiLeDatKeHoach.toFixed(1)}%
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>
                          <div className="font-medium">{item.sanPhamChinh}</div>
                          <div className="text-xs text-gray-500">
                            {(item.sanLuongSanPhamChinh / 1000).toFixed(0)}K kg
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className="font-medium text-blue-600">{item.chatLuongTrungBinh}%</span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className="font-medium text-purple-600">{item.hieuSuatMayMoc}%</span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center">
                          <Users className="w-4 h-4 text-gray-400 mr-1" />
                          <span className="font-medium">{item.soNhanVien}</span>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => openDetailModal(item)}
                            className="text-blue-600 hover:text-blue-800"
                            title="Xem chi tiết"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-800" title="Xuất báo cáo">
                            <Download className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Detail Modal */}
        {isDetailModalOpen && selectedItem && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-800">Chi tiết thông tin</h2>
                  <button
                    onClick={closeDetailModal}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {Object.entries(selectedItem).map(([key, value]) => (
                    <div key={key} className="bg-gray-50 p-4 rounded-lg">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {key.charAt(0).toUpperCase() + key.slice(1)}
                      </label>
                      <p className="text-sm text-gray-900">{String(value)}</p>
                    </div>
                  ))}
                </div>

                <div className="flex justify-end gap-4 mt-6">
                  <button
                    onClick={closeDetailModal}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Đóng
                  </button>
                  <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    Chỉnh sửa
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductionDepartment;
