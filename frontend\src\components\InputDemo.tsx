import { useState } from "react";
import Input from "./Input"; // import component bạn đã viết

export default function InputDemo() {
  const [text, setText] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);

  return (
    <div className="max-w-md mx-auto mt-10 space-y-4">
      {/* Input bình thường */}
      <Input
        label="Tên người dùng"
        placeholder="Nhập tên..."
        icon="user"
        value={text}
        onChange={(e) => setText(e.target.value)}
      />

      {/* Input email */}
      <Input
        label="Email"
        type="email"
        placeholder="Nhập email..."
        icon="email"
      />

      {/* Input password với toggle */}
      <Input
        label="Mật khẩu"
        type={showPassword ? "text" : "password"}
        placeholder="Nhập mật khẩu..."
        icon="password"
        showPasswordToggle
        showPassword={showPassword}
        onTogglePassword={() => setShowPassword(!showPassword)}
        value={password}
        onChange={(e) => setPassword(e.target.value)}
      />

      {/* Input với error state */}
      <Input
        label="Email (sai)"
        type="email"
        placeholder="Nhập email..."
        icon="email"
        error="Email không hợp lệ"
      />
    </div>
  );
}
