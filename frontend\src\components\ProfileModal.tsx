import React, { useState, useEffect } from 'react';
import { 
  X,
  User, 
  Phone, 
  CreditCard, 
  MapPin, 
  Briefcase, 
  Award, 
  FileText, 
  Activity,
  Edit,
  Eye,
  Download,
  Star,
  TrendingUp,
  Calendar,
  Weight,
  Ruler,
  Shirt,
  DollarSign
} from 'lucide-react';
import { User as UserType } from '../types/auth';
import { getDepartmentDisplayName } from '../utils/permissions';

interface ProfileModalProps {
  user: UserType;
  isOpen: boolean;
  onClose: () => void;
}

const ProfileModal: React.FC<ProfileModalProps> = ({ user, isOpen, onClose }) => {
  const [activeTab, setActiveTab] = useState('basic');

  const departmentName = getDepartmentDisplayName(user.department);

  // Close modal on ESC key
  useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEsc);
      document.body.style.overflow = 'hidden'; // Prevent background scroll
    }

    return () => {
      document.removeEventListener('keydown', handleEsc);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // Format currency
  const formatCurrency = (amount?: number) => {
    if (!amount) return 'Chưa cập nhật';
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  // Get status color
  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'Đang làm việc': return 'bg-green-100 text-green-800';
      case 'Nghỉ phép': return 'bg-yellow-100 text-yellow-800';
      case 'Tạm nghỉ': return 'bg-orange-100 text-orange-800';
      case 'Đã nghỉ việc': return 'bg-red-100 text-red-800';
      case 'Thử việc': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Get evaluation stars
  const renderStars = (score?: number) => {
    if (!score) return <span className="text-gray-400">Chưa đánh giá</span>;
    const stars = [];
    const fullStars = Math.floor(score);
    const hasHalfStar = score % 1 !== 0;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(<Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />);
      } else if (i === fullStars && hasHalfStar) {
        stars.push(<Star key={i} className="w-4 h-4 fill-yellow-200 text-yellow-400" />);
      } else {
        stars.push(<Star key={i} className="w-4 h-4 text-gray-300" />);
      }
    }

    return (
      <div className="flex items-center space-x-1">
        {stars}
        <span className="ml-2 text-sm font-medium text-gray-700">{score}/5.0</span>
      </div>
    );
  };

  const tabs = [
    { id: 'basic', name: 'Cơ bản', icon: <User className="w-4 h-4" /> },
    { id: 'physical', name: 'Vật lý', icon: <Ruler className="w-4 h-4" /> },
    { id: 'work', name: 'Công việc', icon: <Briefcase className="w-4 h-4" /> },
    { id: 'documents', name: 'Hồ sơ', icon: <FileText className="w-4 h-4" /> }
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                  <span className="text-lg font-bold text-white">
                    {user.firstName?.charAt(0)}{user.lastName?.charAt(0)}
                  </span>
                </div>
                <div>
                  <h2 className="text-xl font-bold text-white">
                    {user.firstName} {user.lastName}
                  </h2>
                  <p className="text-blue-100">{user.position}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className="px-2 py-1 bg-blue-500 text-white rounded-full text-xs font-medium">
                      {user.employeeCode}
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(user.employeeStatus)}`}>
                      {user.employeeStatus || 'Chưa cập nhật'}
                    </span>
                  </div>
                </div>
              </div>
              <button
                onClick={onClose}
                className="p-2 bg-white bg-opacity-20 rounded-lg hover:bg-opacity-30 transition-colors text-white"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-200 px-6">
            <nav className="flex space-x-6">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  {tab.icon}
                  <span>{tab.name}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="p-6 max-h-[60vh] overflow-y-auto">
            {activeTab === 'basic' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <User className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Họ và tên</p>
                      <p className="text-sm text-gray-600">{user.firstName} {user.lastName}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <User className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Giới tính</p>
                      <p className="text-sm text-gray-600">{user.gender || 'Chưa cập nhật'}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Briefcase className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Bộ phận</p>
                      <p className="text-sm text-gray-600">{departmentName}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <MapPin className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Phòng ban</p>
                      <p className="text-sm text-gray-600">{user.subDepartment || 'Chưa phân công'}</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Phone className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Số điện thoại</p>
                      <p className="text-sm text-gray-600">{user.personalPhone || 'Chưa cập nhật'}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <CreditCard className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Tài khoản ngân hàng</p>
                      <p className="text-sm text-gray-600">{user.bankAccount || 'Chưa cập nhật'}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <MapPin className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Số tủ cá nhân</p>
                      <p className="text-sm text-gray-600">{user.lockerNumber || 'Chưa phân bổ'}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Calendar className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Ngày tham gia</p>
                      <p className="text-sm text-gray-600">{user.createdAt.toLocaleDateString('vi-VN')}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'physical' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Weight className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Cân nặng</p>
                      <p className="text-sm text-gray-600">{user.weight ? `${user.weight} kg` : 'Chưa cập nhật'}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Ruler className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Chiều cao</p>
                      <p className="text-sm text-gray-600">{user.height ? `${user.height} cm` : 'Chưa cập nhật'}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Shirt className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Size áo</p>
                      <p className="text-sm text-gray-600">{user.shirtSize || 'Chưa cập nhật'}</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Shirt className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Size quần</p>
                      <p className="text-sm text-gray-600">{user.pantSize || 'Chưa cập nhật'}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Shirt className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Size dày/dép</p>
                      <p className="text-sm text-gray-600">{user.shoeSize || 'Chưa cập nhật'}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'work' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <DollarSign className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Mức lương cơ bản</p>
                      <p className="text-sm text-gray-600">{formatCurrency(user.baseSalary)}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <TrendingUp className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Mức KPI</p>
                      <p className="text-sm text-gray-600">{user.kpiLevel ? `${user.kpiLevel}%` : 'Chưa đánh giá'}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <FileText className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Mã trách nhiệm</p>
                      <p className="text-sm text-gray-600">{user.responsibilityCode || 'Chưa phân công'}</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Award className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Bảng đánh giá</p>
                      <div className="mt-1">
                        {renderStars(user.evaluationScore)}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <FileText className="w-5 h-5 text-gray-400 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Ghi chú</p>
                      <p className="text-sm text-gray-600">{user.notes || 'Không có ghi chú'}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'documents' && (
              <div className="space-y-6">
                {/* Activities */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    <Activity className="w-5 h-5 text-blue-600 mr-2" />
                    Hoạt động
                  </h3>
                  <div className="space-y-2">
                    {user.activities && user.activities.length > 0 ? (
                      user.activities.map((activity, index) => (
                        <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <span className="text-sm text-gray-700">{activity}</span>
                        </div>
                      ))
                    ) : (
                      <p className="text-sm text-gray-500">Chưa có hoạt động nào được ghi nhận</p>
                    )}
                  </div>
                </div>

                {/* Documents */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    <FileText className="w-5 h-5 text-green-600 mr-2" />
                    Hồ sơ
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {user.profileDocuments && user.profileDocuments.length > 0 ? (
                      user.profileDocuments.map((doc, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                          <div className="flex items-center space-x-3">
                            <FileText className="w-4 h-4 text-gray-400" />
                            <span className="text-sm text-gray-700">{doc}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <button className="p-1 text-gray-400 hover:text-blue-600 transition-colors">
                              <Eye className="w-4 h-4" />
                            </button>
                            <button className="p-1 text-gray-400 hover:text-green-600 transition-colors">
                              <Download className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))
                    ) : (
                      <p className="text-sm text-gray-500 col-span-2">Chưa có hồ sơ nào được tải lên</p>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileModal;
