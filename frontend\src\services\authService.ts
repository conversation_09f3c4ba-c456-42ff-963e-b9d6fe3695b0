import { LoginRequest, RegisterRequest, AuthResponse, User, UserRole } from '../types/auth';

// Mock users theo phòng ban
const MOCK_USERS = [
  {
    _id: 'admin-001',
    username: 'admin',
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'System',
    role: UserRole.ADMIN,
    department: 'admin',
    position: 'Quản trị hệ thống',
    employeeCode: 'ADM001',
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  // Quality Department - Head
  {
    _id: 'quality-head-001',
    username: 'quality_head',
    email: '<EMAIL>',
    firstName: 'Nguyễn',
    lastName: 'Trưởng Chất lượng',
    role: UserRole.MANAGER,
    department: 'quality',
    subDepartment: undefined, // Trưởng bộ phận không thuộc phòng con cụ thể
    position: 'Trưởng bộ phận Chất lượng',
    employeeCode: 'QLT-HEAD-001',
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  // Quality Department - Personnel
  {
    _id: 'quality-personnel-001',
    username: 'quality_personnel',
    email: '<EMAIL>',
    firstName: 'Trần',
    lastName: 'Nhân sự CL',
    role: UserRole.EMPLOYEE,
    department: 'quality',
    subDepartment: 'personnel',
    position: 'Nhân viên Phòng chất lượng nhân sự',
    employeeCode: 'QLT-PER-001',
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  // Quality Department - Process
  {
    _id: 'quality-process-001',
    username: 'quality_process',
    email: '<EMAIL>',
    firstName: 'Lê',
    lastName: 'Quy trình CL',
    role: UserRole.EMPLOYEE,
    department: 'quality',
    subDepartment: 'process',
    position: 'Nhân viên Phòng chất lượng quy trình',
    employeeCode: 'QLT-PRO-001',
    gender: 'Nam',
    weight: 68,
    height: 172,
    shirtSize: 'L',
    pantSize: '32',
    shoeSize: '42',
    personalPhone: '**********',
    bankAccount: '*************',
    lockerNumber: 'L-025',
    employeeStatus: 'Đang làm việc',
    baseSalary: ********,
    kpiLevel: 85,
    responsibilityCode: 'QLT-QT-001',
    evaluationScore: 4.2,
    notes: 'Nhân viên tích cực, có kinh nghiệm trong kiểm soát chất lượng quy trình',
    activities: ['Kiểm tra quy trình sản xuất', 'Đào tạo nhân viên mới', 'Báo cáo chất lượng'],
    profileDocuments: ['CV.pdf', 'Bang_cap.pdf', 'Chung_chi_an_toan.pdf'],
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: 'production-001',
    username: 'production',
    email: '<EMAIL>',
    firstName: 'Trần',
    lastName: 'Sản xuất',
    role: UserRole.EMPLOYEE,
    department: 'production',
    position: 'Nhân viên Sản xuất',
    employeeCode: 'PRD001',
    gender: 'Nữ',
    weight: 55,
    height: 165,
    shirtSize: 'M',
    pantSize: '28',
    shoeSize: '38',
    personalPhone: '**********',
    bankAccount: '*************',
    lockerNumber: 'L-042',
    employeeStatus: 'Đang làm việc',
    baseSalary: ********,
    kpiLevel: 92,
    responsibilityCode: 'PRD-SX-001',
    evaluationScore: 4.5,
    notes: 'Nhân viên xuất sắc, có kỹ năng vận hành máy móc tốt',
    activities: ['Vận hành dây chuyền sản xuất', 'Kiểm tra chất lượng sản phẩm', 'Bảo trì thiết bị'],
    profileDocuments: ['CV.pdf', 'Chung_chi_van_hanh.pdf', 'Bao_hiem_xa_hoi.pdf'],
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: 'business-001',
    username: 'business',
    email: '<EMAIL>',
    firstName: 'Lê',
    lastName: 'Kinh doanh',
    role: UserRole.EMPLOYEE,
    department: 'business',
    position: 'Nhân viên Kinh doanh',
    employeeCode: 'BUS001',
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: 'accounting-001',
    username: 'accounting',
    email: '<EMAIL>',
    firstName: 'Phạm',
    lastName: 'Kế toán',
    role: UserRole.EMPLOYEE,
    department: 'accounting',
    position: 'Nhân viên Kế toán',
    employeeCode: 'ACC001',
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: 'purchasing-001',
    username: 'purchasing',
    email: '<EMAIL>',
    firstName: 'Hoàng',
    lastName: 'Thu mua',
    role: UserRole.EMPLOYEE,
    department: 'purchasing',
    position: 'Nhân viên Thu mua',
    employeeCode: 'PUR001',
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: 'technical-001',
    username: 'technical',
    email: '<EMAIL>',
    firstName: 'Vũ',
    lastName: 'Kỹ thuật',
    role: UserRole.EMPLOYEE,
    department: 'technical',
    position: 'Nhân viên Kỹ thuật',
    employeeCode: 'TEC001',
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

const MOCK_CREDENTIALS = [
  { email: '<EMAIL>', password: '123123' },
  // Quality Department
  { email: '<EMAIL>', password: '123123' },
  { email: '<EMAIL>', password: '123123' },
  { email: '<EMAIL>', password: '123123' },
  // Other departments
  { email: '<EMAIL>', password: '123123' },
  { email: '<EMAIL>', password: '123123' },
  { email: '<EMAIL>', password: '123123' },
  { email: '<EMAIL>', password: '123123' },
  { email: '<EMAIL>', password: '123123' }
];

class AuthService {
  static async login(credentials: LoginRequest): Promise<AuthResponse> {
    try {
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check credentials
      const mockCredential = MOCK_CREDENTIALS.find(
        cred => cred.email === credentials.email && cred.password === credentials.password
      );

      if (mockCredential) {
        const user = MOCK_USERS.find(u => u.email === mockCredential.email);
        if (user) {
          const authResponse: AuthResponse = {
            user,
            accessToken: 'mock-access-token-' + Date.now(),
            refreshToken: 'mock-refresh-token-' + Date.now()
          };

          // Store in localStorage
          localStorage.setItem('accessToken', authResponse.accessToken);
          localStorage.setItem('refreshToken', authResponse.refreshToken);
          localStorage.setItem('user', JSON.stringify(authResponse.user));

          console.log('🎉 Login successful for:', user.email, 'Role:', user.role);
          return authResponse;
        }
      }

      throw new Error('Email hoặc mật khẩu không đúng');
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  static async register(userData: RegisterRequest): Promise<AuthResponse> {
    throw new Error('Đăng ký tài khoản hiện chưa được hỗ trợ');
  }

  static async logout(): Promise<void> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      console.log('🚪 Logout successful');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
    }
  }

  static async refreshToken(): Promise<string | null> {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      if (!refreshToken) return null;

      const newAccessToken = 'mock-access-token-' + Date.now();
      const newRefreshToken = 'mock-refresh-token-' + Date.now();

      localStorage.setItem('accessToken', newAccessToken);
      localStorage.setItem('refreshToken', newRefreshToken);

      return newAccessToken;
    } catch (error) {
      console.error('Token refresh error:', error);
      this.logout();
      return null;
    }
  }

  static getCurrentUser(): User | null {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }

  static getAccessToken(): string | null {
    return localStorage.getItem('accessToken');
  }

  static isAuthenticated(): boolean {
    return !!this.getAccessToken() && !!this.getCurrentUser();
  }
}

export default AuthService;