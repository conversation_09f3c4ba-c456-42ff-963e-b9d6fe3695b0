import React, { useState } from 'react';
import {
  Factory,
  Calendar,
  Package,
  Truck,
  Plus,
  Search,
  Filter,
  Download,
  Edit,
  Eye,
  Trash2,
  BarChart3
} from 'lucide-react';

const ProductionManagement = () => {
  const [activeTab, setActiveTab] = useState<'plans' | 'orders' | 'inventory' | 'shipping'>('plans');

  // Dữ liệu mẫu cho KẾ HOẠCH SẢN XUẤT
  const planData = [
    {
      id: 1,
      maKeHoach: 'KH001',
      tenKeHoach: 'K<PERSON> hoạch sản xuất khoai tây chiên Q2/2024',
      sanPham: '<PERSON><PERSON>ai tây chiên đông lạnh',
      maSanPham: 'SP001',
      soLuongKeHoach: 50000,
      donViTinh: 'kg',
      ngayBatDau: '2024-04-01',
      ngayKetThuc: '2024-06-30',
      trangThai: '<PERSON>ang thực hiện',
      mucDoUuTien: 'Cao',
      nguoiPhuTrach: '<PERSON><PERSON><PERSON><PERSON>',
      phongBan: 'Sản xuất',
      tien<PERSON>o<PERSON><PERSON><PERSON>ai: '65%',
      soLuongDaSanXuat: 32500,
      soLuongConLai: 17500,
      chiPhiUocTinh: 2500000000,
      ghiChu: 'Đơn hàng xuất khẩu Nhật Bản'
    },
    {
      id: 2,
      maKeHoach: 'KH002',
      tenKeHoach: 'Kế hoạch sản xuất cà rốt baby Q2/2024',
      sanPham: 'Cà rốt baby đóng gói',
      maSanPham: 'SP002',
      soLuongKeHoach: 30000,
      donViTinh: 'kg',
      ngayBatDau: '2024-04-15',
      ngayKetThuc: '2024-07-15',
      trangThai: 'Chờ bắt đầu',
      mucDoUuTien: 'Trung bình',
      nguoiPhuTrach: 'Trần Thị Lan',
      phongBan: 'Sản xuất',
      tienDoHienTai: '0%',
      soLuongDaSanXuat: 0,
      soLuongConLai: 30000,
      chiPhiUocTinh: **********,
      ghiChu: 'Chờ nguyên liệu từ nhà cung cấp'
    }
  ];

  // Dữ liệu mẫu cho LỆNH SẢN XUẤT
  const orderData = [
    {
      id: 1,
      maLenhSanXuat: 'LSX001',
      tenLenhSanXuat: 'Lệnh sản xuất khoai tây chiên - Lô 240315',
      maKeHoach: 'KH001',
      sanPham: 'Khoai tây chiên đông lạnh',
      soLuong: 5000,
      donViTinh: 'kg',
      ngayTao: '2024-03-15',
      ngayBatDau: '2024-03-16',
      ngayKetThuc: '2024-03-20',
      trangThai: 'Hoàn thành',
      nguoiTao: 'Nguyễn Văn Minh',
      caTruc: 'Ca 1 (6:00-14:00)',
      mayMoc: 'Dây chuyền 1',
      soLuongHoanThanh: 5000,
      tyLeHoanThanh: '100%',
      chatLuong: 'Đạt',
      ghiChu: 'Hoàn thành đúng tiến độ'
    },
    {
      id: 2,
      maLenhSanXuat: 'LSX002',
      tenLenhSanXuat: 'Lệnh sản xuất khoai tây chiên - Lô 240320',
      maKeHoach: 'KH001',
      sanPham: 'Khoai tây chiên đông lạnh',
      soLuong: 5000,
      donViTinh: 'kg',
      ngayTao: '2024-03-20',
      ngayBatDau: '2024-03-21',
      ngayKetThuc: '2024-03-25',
      trangThai: 'Đang sản xuất',
      nguoiTao: 'Nguyễn Văn Minh',
      caTruc: 'Ca 2 (14:00-22:00)',
      mayMoc: 'Dây chuyền 2',
      soLuongHoanThanh: 3200,
      tyLeHoanThanh: '64%',
      chatLuong: 'Đang kiểm tra',
      ghiChu: 'Đang trong quá trình sản xuất'
    }
  ];

  // Dữ liệu mẫu cho TỒN KHO SẢN XUẤT
  const inventoryData = [
    {
      id: 1,
      maSanPham: 'SP001',
      tenSanPham: 'Khoai tây chiên đông lạnh',
      loaiSanPham: 'Thành phẩm',
      soLuongTon: 15000,
      donViTinh: 'kg',
      viTriKho: 'Kho lạnh A1',
      ngayNhapKho: '2024-03-20',
      hanSuDung: '2024-09-20',
      trangThai: 'Còn hạn',
      giaTriTon: 750000000,
      mucTonToiThieu: 5000,
      mucTonToiDa: 50000,
      nhaCungCap: 'Sản xuất nội bộ',
      ghiChu: 'Chất lượng tốt, sẵn sàng xuất kho'
    },
    {
      id: 2,
      maSanPham: 'NL001',
      tenSanPham: 'Khoai tây tươi',
      loaiSanPham: 'Nguyên liệu',
      soLuongTon: 8000,
      donViTinh: 'kg',
      viTriKho: 'Kho nguyên liệu B2',
      ngayNhapKho: '2024-03-18',
      hanSuDung: '2024-04-18',
      trangThai: 'Còn hạn',
      giaTriTon: 160000000,
      mucTonToiThieu: 2000,
      mucTonToiDa: 20000,
      nhaCungCap: 'Nông sản Đồng Tháp',
      ghiChu: 'Chất lượng A, đã kiểm tra QC'
    }
  ];

  // Dữ liệu mẫu cho XUẤT HÀNG
  const shippingData = [
    {
      id: 1,
      maXuatHang: 'XH001',
      tenDonHang: 'Xuất hàng khoai tây chiên - Đơn hàng JP001',
      khachHang: 'ABC Foods Japan',
      sanPham: 'Khoai tây chiên đông lạnh',
      soLuong: 10000,
      donViTinh: 'kg',
      ngayXuat: '2024-03-22',
      ngayGiaoHang: '2024-03-25',
      trangThai: 'Đã giao',
      phuongThucVanChuyen: 'Container lạnh',
      donViVanChuyen: 'Sài Gòn Express',
      soContainer: 'CONT240322001',
      cangDen: 'Cảng Tokyo',
      nguoiPhuTrach: 'Lê Văn Hùng',
      giaTriDonHang: 500000000,
      ghiChu: 'Giao hàng thành công, khách hàng hài lòng'
    },
    {
      id: 2,
      maXuatHang: 'XH002',
      tenDonHang: 'Xuất hàng cà rốt baby - Đơn hàng VN002',
      khachHang: 'Siêu thị BigC',
      sanPham: 'Cà rốt baby đóng gói',
      soLuong: 2000,
      donViTinh: 'kg',
      ngayXuat: '2024-03-23',
      ngayGiaoHang: '2024-03-24',
      trangThai: 'Đang vận chuyển',
      phuongThucVanChuyen: 'Xe tải lạnh',
      donViVanChuyen: 'Vận tải Miền Nam',
      soContainer: 'XE240323002',
      cangDen: 'TP. Hồ Chí Minh',
      nguoiPhuTrach: 'Phạm Thị Mai',
      giaTriDonHang: 80000000,
      ghiChu: 'Đang trên đường giao hàng'
    }
  ];

  // State for modals
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);

  const openDetailModal = (item: any) => {
    setSelectedItem(item);
    setIsDetailModalOpen(true);
  };

  const closeDetailModal = () => {
    setIsDetailModalOpen(false);
    setSelectedItem(null);
  };

  const tabs = [
    { id: 'plans', name: 'KẾ HOẠCH SẢN XUẤT', icon: <Calendar className="w-4 h-4" />, count: planData.length },
    { id: 'orders', name: 'LỆNH SẢN XUẤT', icon: <Factory className="w-4 h-4" />, count: orderData.length },
    { id: 'inventory', name: 'TỒN KHO SẢN XUẤT', icon: <Package className="w-4 h-4" />, count: inventoryData.length },
    { id: 'shipping', name: 'XUẤT HÀNG', icon: <Truck className="w-4 h-4" />, count: shippingData.length }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2 flex items-center">
            <Factory className="w-8 h-8 text-blue-600 mr-3" />
            Quản lý sản xuất
          </h1>
          <p className="text-gray-600">Quản lý kế hoạch, lệnh sản xuất, tồn kho và xuất hàng</p>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
          {/* Tổng quan kế hoạch */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <Calendar className="w-5 h-5 text-blue-600 mr-2" />
              Tổng quan kế hoạch
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Tổng kế hoạch</span>
                <span className="text-lg font-bold text-blue-600">{planData.length}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Đang thực hiện</span>
                <span className="text-lg font-bold text-green-600">
                  {planData.filter(item => item.trangThai === 'Đang thực hiện').length}
                </span>
              </div>
            </div>
          </div>

          {/* Tổng quan lệnh sản xuất */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <Factory className="w-5 h-5 text-green-600 mr-2" />
              Tổng quan lệnh SX
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Tổng lệnh SX</span>
                <span className="text-lg font-bold text-green-600">{orderData.length}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Đang sản xuất</span>
                <span className="text-lg font-bold text-blue-600">
                  {orderData.filter(item => item.trangThai === 'Đang sản xuất').length}
                </span>
              </div>
            </div>
          </div>

          {/* Tổng quan tồn kho */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <Package className="w-5 h-5 text-purple-600 mr-2" />
              Tổng quan tồn kho
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Tổng mặt hàng</span>
                <span className="text-lg font-bold text-purple-600">{inventoryData.length}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Giá trị tồn</span>
                <span className="text-lg font-bold text-green-600">
                  {(inventoryData.reduce((sum, item) => sum + item.giaTriTon, 0) / 1000000000).toFixed(1)}B
                </span>
              </div>
            </div>
          </div>

          {/* Tổng quan xuất hàng */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <Truck className="w-5 h-5 text-orange-600 mr-2" />
              Tổng quan xuất hàng
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Đơn xuất hàng</span>
                <span className="text-lg font-bold text-orange-600">{shippingData.length}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Đã giao</span>
                <span className="text-lg font-bold text-green-600">
                  {shippingData.filter(item => item.trangThai === 'Đã giao').length}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.icon}
                  {tab.name}
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    activeTab === tab.id ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                  }`}>
                    {tab.count}
                  </span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Action Bar */}
        <div className="mb-6 bg-white rounded-lg shadow-sm p-4">
          <div className="flex flex-wrap gap-4 items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Tìm kiếm sản xuất..."
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-64"
                />
              </div>
              <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                <Filter className="h-4 w-4" />
                Lọc
              </button>
            </div>
            <div className="flex items-center gap-2">
              <button className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                <Download className="h-4 w-4" />
                Xuất Excel
              </button>
              <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                <Plus className="h-4 w-4" />
                Thêm mới
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="bg-white rounded-lg shadow-sm">
          {/* KẾ HOẠCH SẢN XUẤT */}
          {activeTab === 'plans' && (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã KH</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên kế hoạch</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sản phẩm</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Số lượng KH</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày bắt đầu</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Người phụ trách</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tiến độ</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hoạt động</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {planData.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{item.maKeHoach}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.tenKeHoach}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.sanPham}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.soLuongKeHoach.toLocaleString()} {item.donViTinh}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.ngayBatDau}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.nguoiPhuTrach}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center">
                          <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full"
                              style={{ width: item.tienDoHienTai }}
                            ></div>
                          </div>
                          <span className="text-xs font-medium">{item.tienDoHienTai}</span>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.trangThai === 'Đang thực hiện' ? 'bg-blue-100 text-blue-800' :
                          item.trangThai === 'Hoàn thành' ? 'bg-green-100 text-green-800' :
                          item.trangThai === 'Chờ bắt đầu' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {item.trangThai}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => openDetailModal(item)}
                            className="text-blue-600 hover:text-blue-800"
                            title="Xem chi tiết"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-800" title="Chỉnh sửa">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="text-red-600 hover:text-red-800" title="Xóa">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* LỆNH SẢN XUẤT */}
          {activeTab === 'orders' && (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã LSX</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên lệnh SX</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sản phẩm</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Số lượng</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ca trực</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Máy móc</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tỷ lệ HT</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hoạt động</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {orderData.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{item.maLenhSanXuat}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.tenLenhSanXuat}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.sanPham}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.soLuong.toLocaleString()} {item.donViTinh}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.caTruc}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.mayMoc}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center">
                          <BarChart3 className="w-4 h-4 text-blue-400 mr-1" />
                          <span className="font-medium">{item.tyLeHoanThanh}</span>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.trangThai === 'Hoàn thành' ? 'bg-green-100 text-green-800' :
                          item.trangThai === 'Đang sản xuất' ? 'bg-blue-100 text-blue-800' :
                          item.trangThai === 'Chờ sản xuất' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {item.trangThai}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => openDetailModal(item)}
                            className="text-blue-600 hover:text-blue-800"
                            title="Xem chi tiết"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-800" title="Chỉnh sửa">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="text-red-600 hover:text-red-800" title="Xóa">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* TỒN KHO SẢN XUẤT */}
          {activeTab === 'inventory' && (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã SP</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên sản phẩm</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loại</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Số lượng tồn</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vị trí kho</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hạn sử dụng</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Giá trị tồn</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hoạt động</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {inventoryData.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{item.maSanPham}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.tenSanPham}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.loaiSanPham === 'Thành phẩm' ? 'bg-green-100 text-green-800' :
                          item.loaiSanPham === 'Nguyên liệu' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {item.loaiSanPham}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.soLuongTon.toLocaleString()} {item.donViTinh}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.viTriKho}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.hanSuDung}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className="font-medium text-green-600">
                          {(item.giaTriTon / 1000000).toLocaleString()}M VNĐ
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.trangThai === 'Còn hạn' ? 'bg-green-100 text-green-800' :
                          item.trangThai === 'Gần hết hạn' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {item.trangThai}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => openDetailModal(item)}
                            className="text-blue-600 hover:text-blue-800"
                            title="Xem chi tiết"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-800" title="Chỉnh sửa">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="text-red-600 hover:text-red-800" title="Xóa">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* XUẤT HÀNG */}
          {activeTab === 'shipping' && (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã XH</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên đơn hàng</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Khách hàng</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sản phẩm</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Số lượng</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày xuất</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vận chuyển</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hoạt động</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {shippingData.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{item.maXuatHang}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.tenDonHang}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.khachHang}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.sanPham}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.soLuong.toLocaleString()} {item.donViTinh}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.ngayXuat}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.phuongThucVanChuyen}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.trangThai === 'Đã giao' ? 'bg-green-100 text-green-800' :
                          item.trangThai === 'Đang vận chuyển' ? 'bg-blue-100 text-blue-800' :
                          item.trangThai === 'Chờ xuất hàng' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {item.trangThai}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => openDetailModal(item)}
                            className="text-blue-600 hover:text-blue-800"
                            title="Xem chi tiết"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-800" title="Chỉnh sửa">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="text-red-600 hover:text-red-800" title="Xóa">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Detail Modal */}
        {isDetailModalOpen && selectedItem && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-800">Chi tiết thông tin</h2>
                  <button
                    onClick={closeDetailModal}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {Object.entries(selectedItem).map(([key, value]) => (
                    <div key={key} className="bg-gray-50 p-4 rounded-lg">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {key.charAt(0).toUpperCase() + key.slice(1)}
                      </label>
                      <p className="text-sm text-gray-900">{String(value)}</p>
                    </div>
                  ))}
                </div>

                <div className="flex justify-end gap-4 mt-6">
                  <button
                    onClick={closeDetailModal}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Đóng
                  </button>
                  <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    Chỉnh sửa
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductionManagement;
