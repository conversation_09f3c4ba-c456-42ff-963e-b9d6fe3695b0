import React, { useState } from "react";
import {
  Clock,
  Target,
  CheckSquare,
  Calendar,
  AlertTriangle,
  TrendingUp,
  Users,
  FileText,
  Bell,
  Award,
  BarChart3,
  Activity,
  User
} from "lucide-react";
import { useAuth } from "../contexts/AuthContext";
import { getDepartmentDisplayName } from "../utils/permissions";
import ProfileModal from "../components/ProfileModal";
import AttendanceModal from "../components/AttendanceModal";
import LeaveRequestModal from "../components/LeaveRequestModal";

// Personal Stats for Employee
const getPersonalStats = (department: string) => {
  const baseStats = [
    { 
      label: "Nhiệm vụ hôm nay", 
      value: "8", 
      total: "12",
      icon: <CheckSquare className="w-5 h-5" />,
      color: "from-blue-500 to-blue-600",
      textColor: "text-blue-600"
    },
    { 
      label: "Hoàn thành tuần", 
      value: "85%", 
      total: "100%",
      icon: <Target className="w-5 h-5" />,
      color: "from-green-500 to-green-600",
      textColor: "text-green-600"
    },
    { 
      label: "Thông báo mới", 
      value: "3", 
      total: "5",
      icon: <Bell className="w-5 h-5" />,
      color: "from-orange-500 to-orange-600",
      textColor: "text-orange-600"
    },
    { 
      label: "Đánh giá tháng", 
      value: "4.2", 
      total: "5.0",
      icon: <Award className="w-5 h-5" />,
      color: "from-purple-500 to-purple-600",
      textColor: "text-purple-600"
    }
  ];

  return baseStats;
};

// Recent Activities for Employee
const getRecentActivities = (department: string) => {
  const activities = [
    {
      id: 1,
      title: "Hoàn thành báo cáo tuần",
      description: "Báo cáo công việc tuần 47/2024",
      time: "2 giờ trước",
      type: "completed",
      icon: <FileText className="w-4 h-4" />
    },
    {
      id: 2,
      title: "Tham gia họp phòng ban",
      description: "Họp review kết quả tháng 11",
      time: "1 ngày trước",
      type: "meeting",
      icon: <Users className="w-4 h-4" />
    },
    {
      id: 3,
      title: "Cập nhật tiến độ dự án",
      description: "Dự án cải tiến quy trình",
      time: "2 ngày trước",
      type: "update",
      icon: <TrendingUp className="w-4 h-4" />
    },
    {
      id: 4,
      title: "Nhận nhiệm vụ mới",
      description: "Kiểm tra chất lượng sản phẩm",
      time: "3 ngày trước",
      type: "task",
      icon: <CheckSquare className="w-4 h-4" />
    }
  ];

  return activities;
};

// Today's Schedule
const getTodaySchedule = () => {
  return [
    {
      time: "08:00",
      title: "Họp đầu ca",
      description: "Briefing công việc ngày",
      status: "completed"
    },
    {
      time: "09:30",
      title: "Kiểm tra chất lượng",
      description: "Kiểm tra lô hàng #2024-1147",
      status: "in-progress"
    },
    {
      time: "14:00",
      title: "Báo cáo tiến độ",
      description: "Báo cáo với trưởng phòng",
      status: "pending"
    },
    {
      time: "16:00",
      title: "Đào tạo an toàn",
      description: "Khóa đào tạo an toàn lao động",
      status: "pending"
    }
  ];
};

// Quick Actions for Employee
const getQuickActions = (department: string) => {
  return [
    {
      title: "Chấm công",
      description: "Chấm công vào/ra ca",
      icon: <Clock className="w-6 h-6" />,
      color: "bg-blue-500",
      action: "attendance"
    },
    {
      title: "Báo cáo công việc",
      description: "Gửi báo cáo hàng ngày",
      icon: <FileText className="w-6 h-6" />,
      color: "bg-green-500",
      action: "report"
    },
    {
      title: "Xin nghỉ phép",
      description: "Đăng ký nghỉ phép",
      icon: <Calendar className="w-6 h-6" />,
      color: "bg-orange-500",
      action: "leave"
    },
    {
      title: "Thông tin cá nhân",
      description: "Xem hồ sơ chi tiết",
      icon: <User className="w-6 h-6" />,
      color: "bg-purple-500",
      action: "profile"
    }
  ];
};

// Component for Personal Stat Card
const PersonalStatCard: React.FC<{ stat: any }> = ({ stat }) => (
  <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm font-medium text-gray-600 mb-1">{stat.label}</p>
        <div className="flex items-baseline space-x-2">
          <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
          {stat.total && (
            <p className="text-sm text-gray-500">/ {stat.total}</p>
          )}
        </div>
      </div>
      <div className={`p-3 rounded-lg bg-gradient-to-r ${stat.color}`}>
        <div className="text-white">
          {stat.icon}
        </div>
      </div>
    </div>
  </div>
);

// Component for Quick Action Card
const QuickActionCard: React.FC<{
  action: any;
  onProfileClick?: () => void;
  onAttendanceClick?: () => void;
  onLeaveRequestClick?: () => void;
}> = ({ action, onProfileClick, onAttendanceClick, onLeaveRequestClick }) => (
  <div
    onClick={() => {
      if (action.action === 'profile' && onProfileClick) {
        onProfileClick();
      } else if (action.action === 'attendance' && onAttendanceClick) {
        onAttendanceClick();
      } else if (action.action === 'leave' && onLeaveRequestClick) {
        onLeaveRequestClick();
      }
    }}
    className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-all cursor-pointer group"
  >
    <div className="flex items-center space-x-4">
      <div className={`p-3 rounded-lg ${action.color} group-hover:scale-110 transition-transform`}>
        <div className="text-white">
          {action.icon}
        </div>
      </div>
      <div>
        <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
          {action.title}
        </h3>
        <p className="text-sm text-gray-600">{action.description}</p>
      </div>
    </div>
  </div>
);

// Component for Activity Item
const ActivityItem: React.FC<{ activity: any }> = ({ activity }) => (
  <div className="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
    <div className="flex-shrink-0 p-2 bg-blue-100 rounded-lg">
      <div className="text-blue-600">
        {activity.icon}
      </div>
    </div>
    <div className="flex-1 min-w-0">
      <p className="text-sm font-medium text-gray-900">{activity.title}</p>
      <p className="text-sm text-gray-600">{activity.description}</p>
      <p className="text-xs text-gray-400 mt-1">{activity.time}</p>
    </div>
  </div>
);

// Component for Schedule Item
const ScheduleItem: React.FC<{ item: any }> = ({ item }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in-progress': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'Hoàn thành';
      case 'in-progress': return 'Đang thực hiện';
      case 'pending': return 'Chờ thực hiện';
      default: return 'Chờ thực hiện';
    }
  };

  return (
    <div className="flex items-center space-x-4 p-3 hover:bg-gray-50 rounded-lg transition-colors">
      <div className="text-sm font-mono text-gray-500 w-12">
        {item.time}
      </div>
      <div className="flex-1">
        <p className="text-sm font-medium text-gray-900">{item.title}</p>
        <p className="text-xs text-gray-600">{item.description}</p>
      </div>
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
        {getStatusText(item.status)}
      </span>
    </div>
  );
};

const EmployeeDashboard: React.FC = () => {
  const { user } = useAuth();
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const [isAttendanceModalOpen, setIsAttendanceModalOpen] = useState(false);
  const [isLeaveRequestModalOpen, setIsLeaveRequestModalOpen] = useState(false);

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải...</p>
        </div>
      </div>
    );
  }

  const departmentName = getDepartmentDisplayName(user.department);
  const personalStats = getPersonalStats(user.department || '');
  const recentActivities = getRecentActivities(user.department || '');
  const todaySchedule = getTodaySchedule();
  const quickActions = getQuickActions(user.department || '');

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header Section */}
        <div className="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl shadow-xl p-6 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-white">
                    Chào mừng, {user.firstName}!
                  </h1>
                  <p className="text-indigo-100 text-lg mt-1">
                    {user.position} - {departmentName}
                  </p>
                </div>
                <div className="hidden md:block text-right text-white">
                  <p className="text-sm opacity-80">Hôm nay</p>
                  <p className="text-xl font-bold">{new Date().toLocaleDateString('vi-VN')}</p>
                  <p className="text-sm opacity-80">{new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}</p>
                </div>
              </div>
              <div className="flex items-center mt-3 space-x-2">
                <span className="px-3 py-1 bg-indigo-500 text-white rounded-full text-sm font-medium">
                  {user.employeeCode}
                </span>
                {user.subDepartment && (
                  <span className="px-3 py-1 bg-purple-500 text-white rounded-full text-sm">
                    {user.subDepartment.toUpperCase()}
                  </span>
                )}
                <span className="px-3 py-1 bg-green-500 text-white rounded-full text-sm font-medium">
                  {user.employeeStatus || 'Đang làm việc'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Personal Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {personalStats.map((stat, index) => (
            <PersonalStatCard key={index} stat={stat} />
          ))}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Quick Actions & Schedule */}
          <div className="lg:col-span-2 space-y-8">
            {/* Quick Actions */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                <Activity className="w-6 h-6 text-blue-600 mr-2" />
                Thao tác nhanh
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {quickActions.map((action, index) => (
                  <QuickActionCard
                    key={index}
                    action={action}
                    onProfileClick={() => setIsProfileModalOpen(true)}
                    onAttendanceClick={() => setIsAttendanceModalOpen(true)}
                    onLeaveRequestClick={() => setIsLeaveRequestModalOpen(true)}
                  />
                ))}
              </div>
            </div>

            {/* Today's Schedule */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                <Calendar className="w-6 h-6 text-green-600 mr-2" />
                Lịch trình hôm nay
              </h2>
              <div className="space-y-2">
                {todaySchedule.map((item, index) => (
                  <ScheduleItem key={index} item={item} />
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Recent Activities */}
          <div className="space-y-8">
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                <BarChart3 className="w-6 h-6 text-purple-600 mr-2" />
                Hoạt động gần đây
              </h2>
              <div className="space-y-2">
                {recentActivities.map((activity) => (
                  <ActivityItem key={activity.id} activity={activity} />
                ))}
              </div>
              <div className="mt-4 pt-4 border-t border-gray-100">
                <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">
                  Xem tất cả hoạt động →
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Profile Modal */}
        <ProfileModal
          user={user}
          isOpen={isProfileModalOpen}
          onClose={() => setIsProfileModalOpen(false)}
        />

        {/* Attendance Modal */}
        <AttendanceModal
          isOpen={isAttendanceModalOpen}
          onClose={() => setIsAttendanceModalOpen(false)}
          showBackdrop={true}
        />

        {/* Leave Request Modal */}
        <LeaveRequestModal
          isOpen={isLeaveRequestModalOpen}
          onClose={() => setIsLeaveRequestModalOpen(false)}
          showBackdrop={true}
        />
      </div>
    </div>
  );
};

export default EmployeeDashboard;
