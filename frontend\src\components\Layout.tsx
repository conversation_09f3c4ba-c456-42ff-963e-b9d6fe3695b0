import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import Sidebar from './Sidebar';
import UserProfileDropdown from './UserProfileDropdown';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { user } = useAuth();

  return (
    <div className="flex h-screen bg-gray-100">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-1">
          <div className="relative flex items-center justify-center">
            {/* Logo - positioned absolutely to the left */}
            <div className="absolute left-0 flex items-center">
              <img
                src="/logo.png"
                alt="Company Logo"
                className="h-12 w-12 object-contain"
              />
            </div>

            {/* Centered text */}
            <h1 className="text-xl font-semibold text-gray-800 text-center">
              HÃY LÀM VIỆC CỐNG HIẾN VÀ HIỆU QUẢ - LẤY CÔNG VIỆC TẠI CÔNG TY LÀM NIỀM VUI
              <br />ĐÓ LÀ CON ĐƯỜNG DUY NHẤT ỔN ĐỊNH TRONG CUỘC SỐNG
            </h1>

            {/* User dropdown - positioned absolutely to the right */}
            <div className="absolute right-0 flex items-center">
              <UserProfileDropdown />
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto p-6">
          {children}
        </main>
      </div>
    </div>
  );
};

export default Layout;
