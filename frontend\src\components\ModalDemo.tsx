import React, { useState } from 'react';
import { Clock, Calendar, User } from 'lucide-react';
import AttendanceModal from './AttendanceModal';
import LeaveRequestModal from './LeaveRequestModal';

const ModalDemo: React.FC = () => {
  const [isAttendanceModalOpen, setIsAttendanceModalOpen] = useState(false);
  const [isLeaveRequestModalOpen, setIsLeaveRequestModalOpen] = useState(false);

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-lg p-8 max-w-md w-full">
        <h1 className="text-2xl font-bold text-gray-900 mb-6 text-center">
          Demo Modal Forms
        </h1>
        
        <div className="space-y-4">
          {/* Attendance Modal Button */}
          <button
            onClick={() => setIsAttendanceModalOpen(true)}
            className="w-full flex items-center justify-center space-x-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold py-4 px-6 rounded-lg transition-colors"
          >
            <Clock className="w-5 h-5" />
            <span>Mở Form Chấm Công</span>
          </button>

          {/* Leave Request Modal Button */}
          <button
            onClick={() => setIsLeaveRequestModalOpen(true)}
            className="w-full flex items-center justify-center space-x-3 bg-orange-600 hover:bg-orange-700 text-white font-semibold py-4 px-6 rounded-lg transition-colors"
          >
            <Calendar className="w-5 h-5" />
            <span>Mở Form Xin Nghỉ Phép</span>
          </button>
        </div>

        <div className="mt-8 p-4 bg-gray-50 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-2">Hướng dẫn:</h3>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Click vào các nút để mở modal tương ứng</li>
            <li>• Nhấn ESC hoặc click ngoài modal để đóng</li>
            <li>• Form chấm công có tính năng lấy vị trí GPS</li>
            <li>• Form nghỉ phép có validation đầy đủ</li>
          </ul>
        </div>
      </div>

      {/* Attendance Modal */}
      <AttendanceModal
        isOpen={isAttendanceModalOpen}
        onClose={() => setIsAttendanceModalOpen(false)}
      />

      {/* Leave Request Modal */}
      <LeaveRequestModal
        isOpen={isLeaveRequestModalOpen}
        onClose={() => setIsLeaveRequestModalOpen(false)}
      />
    </div>
  );
};

export default ModalDemo;
