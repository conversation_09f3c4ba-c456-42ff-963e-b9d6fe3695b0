import React, { useState } from 'react';
import {
  Users,
  FileText,
  Star,
  DollarSign,
  Plus,
  Search,
  Filter,
  Download,
  Edit,
  Eye,
  Trash2,
  Award
} from 'lucide-react';

const QualityPersonnel = () => {
  const [activeTab, setActiveTab] = useState<'employees' | 'responsibilities' | 'evaluations' | 'salaries'>('employees');

  // D<PERSON> liệu mẫu cho DANH SÁCH NHÂN VIÊN
  const employeeData = [
    {
      id: 1,
      maNhan<PERSON>ien: 'NV001',
      hoTen: 'Nguy<PERSON><PERSON>',
      gioiTinh: 'Nam',
      ngaySinh: '1990-05-15',
      soDienThoai: '0901234567',
      email: '<EMAIL>',
      diaChi: '123 <PERSON><PERSON><PERSON><PERSON>, Q.7, TP.HCM',
      chucVu: 'Nhân viên QC',
      phong<PERSON>an: 'Chất lượng',
      ngayVaoLam: '2022-01-15',
      trangThai: '<PERSON><PERSON> làm việc',
      loaiHopDong: '<PERSON><PERSON><PERSON> thức',
      mucLuong: 15000000,
      trin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: '<PERSON><PERSON><PERSON> học',
      chuy<PERSON><PERSON><PERSON>: '<PERSON><PERSON><PERSON> tra chất lượng th<PERSON>c phẩm',
      kiNangDacBiet: 'HACCP, ISO 22000',
      ghiChu: 'Nhân viên tích cực, có kinh nghiệm'
    },
    {
      id: 2,
      maNhanVien: 'NV002',
      hoTen: 'Trần Thị Bình',
      gioiTinh: 'Nữ',
      ngaySinh: '1992-08-20',
      soDienThoai: '0902345678',
      email: '<EMAIL>',
      diaChi: '456 Lê Văn Việt, Q.9, TP.HCM',
      chucVu: 'Trưởng nhóm QC',
      phongBan: 'Chất lượng',
      ngayVaoLam: '2021-03-10',
      trangThai: 'Đang làm việc',
      loaiHopDong: 'Chính thức',
      mucLuong: 20000000,
      trinhDoHocVan: 'Thạc sĩ',
      chuyenMon: 'Quản lý chất lượng',
      kiNangDacBiet: 'Six Sigma, Lean Manufacturing',
      ghiChu: 'Lãnh đạo tốt, có tầm nhìn'
    }
  ];

  // Dữ liệu mẫu cho DANH SÁCH TRÁCH NHIỆM
  const responsibilityData = [
    {
      id: 1,
      maTrachNhiem: 'TN001',
      tenTrachNhiem: 'Kiểm tra chất lượng nguyên liệu đầu vào',
      moTa: 'Thực hiện kiểm tra, đánh giá chất lượng nguyên liệu trước khi đưa vào sản xuất',
      maNhanVien: 'NV001',
      tenNhanVien: 'Nguyễn Văn An',
      mucDoUuTien: 'Cao',
      thoiGianThucHien: '2 giờ/ngày',
      ngayBatDau: '2024-01-01',
      ngayKetThuc: '2024-12-31',
      trangThai: 'Đang thực hiện',
      tieuChiDanhGia: 'Độ chính xác, tốc độ kiểm tra',
      ketQuaMongDoi: 'Đảm bảo 100% nguyên liệu đạt chuẩn',
      ghiChu: 'Cần cập nhật quy trình định kỳ'
    },
    {
      id: 2,
      maTrachNhiem: 'TN002',
      tenTrachNhiem: 'Giám sát quy trình sản xuất',
      moTa: 'Theo dõi và đảm bảo quy trình sản xuất tuân thủ các tiêu chuẩn chất lượng',
      maNhanVien: 'NV002',
      tenNhanVien: 'Trần Thị Bình',
      mucDoUuTien: 'Cao',
      thoiGianThucHien: '8 giờ/ngày',
      ngayBatDau: '2024-01-01',
      ngayKetThuc: '2024-12-31',
      trangThai: 'Đang thực hiện',
      tieuChiDanhGia: 'Tuân thủ quy trình, phát hiện sai sót',
      ketQuaMongDoi: 'Giảm 50% lỗi sản xuất',
      ghiChu: 'Cần đào tạo thêm về công nghệ mới'
    }
  ];

  // Dữ liệu mẫu cho ĐÁNH GIÁ NHÂN VIÊN
  const evaluationData = [
    {
      id: 1,
      maDanhGia: 'DG001',
      maNhanVien: 'NV001',
      tenNhanVien: 'Nguyễn Văn An',
      kyDanhGia: 'Q1/2024',
      ngayDanhGia: '2024-03-31',
      nguoiDanhGia: 'Trần Thị Bình',
      chucVuNguoiDanhGia: 'Trưởng nhóm QC',
      diemKyNang: 8.5,
      diemThaiDo: 9.0,
      diemHieuQua: 8.0,
      diemTongThe: 8.5,
      xepLoai: 'Tốt',
      diemManh: 'Tỉ mỉ, cẩn thận trong công việc',
      diemCanCaiThien: 'Cần nâng cao kỹ năng giao tiếp',
      keHoachPhatTrien: 'Tham gia khóa đào tạo soft skills',
      mucTieuTiepTheo: 'Trở thành QC leader trong 6 tháng',
      ghiChu: 'Nhân viên có tiềm năng phát triển'
    },
    {
      id: 2,
      maDanhGia: 'DG002',
      maNhanVien: 'NV002',
      tenNhanVien: 'Trần Thị Bình',
      kyDanhGia: 'Q1/2024',
      ngayDanhGia: '2024-03-31',
      nguoiDanhGia: 'Lê Văn Cường',
      chucVuNguoiDanhGia: 'Quản lý chất lượng',
      diemKyNang: 9.0,
      diemThaiDo: 9.5,
      diemHieuQua: 9.0,
      diemTongThe: 9.2,
      xepLoai: 'Xuất sắc',
      diemManh: 'Lãnh đạo tốt, có tầm nhìn chiến lược',
      diemCanCaiThien: 'Cần chia sẻ kiến thức nhiều hơn',
      keHoachPhatTrien: 'Tham gia khóa đào tạo leadership',
      mucTieuTiepTheo: 'Phát triển team QC mạnh hơn',
      ghiChu: 'Ứng viên sáng giá cho vị trí quản lý'
    }
  ];

  // Dữ liệu mẫu cho BẢNG LƯƠNG NHÂN VIÊN
  const salaryData = [
    {
      id: 1,
      maNhanVien: 'NV001',
      tenNhanVien: 'Nguyễn Văn An',
      chucVu: 'Nhân viên QC',
      thangNam: '03/2024',
      luongCoBan: 15000000,
      phuCapChucVu: 1000000,
      phuCapKhac: 500000,
      thuongKPI: 2000000,
      thuongDuAn: 0,
      tongThuNhap: 18500000,
      baoHiemXaHoi: 1110000,
      baoHiemYTe: 277500,
      baoHiemThatNghiep: 185000,
      thueThuNhapCaNhan: 850000,
      tongKhauTru: 2422500,
      thucLinh: 16077500,
      soNgayLam: 22,
      soNgayNghi: 0,
      soGioLamThem: 8,
      ghiChu: 'Hoàn thành tốt nhiệm vụ'
    },
    {
      id: 2,
      maNhanVien: 'NV002',
      tenNhanVien: 'Trần Thị Bình',
      chucVu: 'Trưởng nhóm QC',
      thangNam: '03/2024',
      luongCoBan: 20000000,
      phuCapChucVu: 3000000,
      phuCapKhac: 1000000,
      thuongKPI: 3000000,
      thuongDuAn: 2000000,
      tongThuNhap: 29000000,
      baoHiemXaHoi: 1740000,
      baoHiemYTe: 435000,
      baoHiemThatNghiep: 290000,
      thueThuNhapCaNhan: 2150000,
      tongKhauTru: 4615000,
      thucLinh: 24385000,
      soNgayLam: 22,
      soNgayNghi: 0,
      soGioLamThem: 12,
      ghiChu: 'Xuất sắc trong quản lý team'
    }
  ];

  // State for modals
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);

  const openDetailModal = (item: any) => {
    setSelectedItem(item);
    setIsDetailModalOpen(true);
  };

  const closeDetailModal = () => {
    setIsDetailModalOpen(false);
    setSelectedItem(null);
  };

  const tabs = [
    { id: 'employees', name: 'DANH SÁCH NHÂN VIÊN', icon: <Users className="w-4 h-4" />, count: employeeData.length },
    { id: 'responsibilities', name: 'DANH SÁCH TRÁCH NHIỆM', icon: <FileText className="w-4 h-4" />, count: responsibilityData.length },
    { id: 'evaluations', name: 'ĐÁNH GIÁ NHÂN VIÊN', icon: <Star className="w-4 h-4" />, count: evaluationData.length },
    { id: 'salaries', name: 'BẢNG LƯƠNG NHÂN VIÊN', icon: <DollarSign className="w-4 h-4" />, count: salaryData.length }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2 flex items-center">
            <Users className="w-8 h-8 text-blue-600 mr-3" />
            Phòng chất lượng nhân sự
          </h1>
          <p className="text-gray-600">Quản lý nhân viên, trách nhiệm, đánh giá và lương bổng</p>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
          {/* Tổng quan nhân viên */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <Users className="w-5 h-5 text-blue-600 mr-2" />
              Tổng quan nhân viên
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Tổng nhân viên</span>
                <span className="text-lg font-bold text-blue-600">{employeeData.length}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Đang làm việc</span>
                <span className="text-lg font-bold text-green-600">
                  {employeeData.filter(item => item.trangThai === 'Đang làm việc').length}
                </span>
              </div>
            </div>
          </div>

          {/* Tổng quan trách nhiệm */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <FileText className="w-5 h-5 text-green-600 mr-2" />
              Tổng quan trách nhiệm
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Tổng trách nhiệm</span>
                <span className="text-lg font-bold text-green-600">{responsibilityData.length}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Đang thực hiện</span>
                <span className="text-lg font-bold text-blue-600">
                  {responsibilityData.filter(item => item.trangThai === 'Đang thực hiện').length}
                </span>
              </div>
            </div>
          </div>

          {/* Tổng quan đánh giá */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <Star className="w-5 h-5 text-yellow-600 mr-2" />
              Tổng quan đánh giá
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Đã đánh giá</span>
                <span className="text-lg font-bold text-yellow-600">{evaluationData.length}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Xuất sắc</span>
                <span className="text-lg font-bold text-green-600">
                  {evaluationData.filter(item => item.xepLoai === 'Xuất sắc').length}
                </span>
              </div>
            </div>
          </div>

          {/* Tổng quan lương */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <DollarSign className="w-5 h-5 text-purple-600 mr-2" />
              Tổng quan lương
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Đã tính lương</span>
                <span className="text-lg font-bold text-purple-600">{salaryData.length}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Tổng chi</span>
                <span className="text-lg font-bold text-green-600">
                  {(salaryData.reduce((sum, item) => sum + item.thucLinh, 0) / 1000000).toFixed(1)}M
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.icon}
                  {tab.name}
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    activeTab === tab.id ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                  }`}>
                    {tab.count}
                  </span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Action Bar */}
        <div className="mb-6 bg-white rounded-lg shadow-sm p-4">
          <div className="flex flex-wrap gap-4 items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Tìm kiếm..."
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-64"
                />
              </div>
              <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                <Filter className="h-4 w-4" />
                Lọc
              </button>
            </div>
            <div className="flex items-center gap-2">
              <button className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                <Download className="h-4 w-4" />
                Xuất Excel
              </button>
              <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                <Plus className="h-4 w-4" />
                Thêm mới
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="bg-white rounded-lg shadow-sm">
          {/* DANH SÁCH NHÂN VIÊN */}
          {activeTab === 'employees' && (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã NV</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Họ tên</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Chức vụ</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phòng ban</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Điện thoại</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hoạt động</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {employeeData.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{item.maNhanVien}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.hoTen}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.chucVu}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.phongBan}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.soDienThoai}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.email}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.trangThai === 'Đang làm việc' ? 'bg-green-100 text-green-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {item.trangThai}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => openDetailModal(item)}
                            className="text-blue-600 hover:text-blue-800"
                            title="Xem chi tiết"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-800" title="Chỉnh sửa">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="text-red-600 hover:text-red-800" title="Xóa">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* DANH SÁCH TRÁCH NHIỆM */}
          {activeTab === 'responsibilities' && (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã TN</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên trách nhiệm</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nhân viên</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mức độ ưu tiên</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thời gian</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày bắt đầu</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hoạt động</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {responsibilityData.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{item.maTrachNhiem}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.tenTrachNhiem}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.tenNhanVien}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.mucDoUuTien === 'Cao' ? 'bg-red-100 text-red-800' :
                          item.mucDoUuTien === 'Trung bình' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {item.mucDoUuTien}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.thoiGianThucHien}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.ngayBatDau}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.trangThai === 'Đang thực hiện' ? 'bg-blue-100 text-blue-800' :
                          item.trangThai === 'Hoàn thành' ? 'bg-green-100 text-green-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {item.trangThai}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => openDetailModal(item)}
                            className="text-blue-600 hover:text-blue-800"
                            title="Xem chi tiết"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-800" title="Chỉnh sửa">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="text-red-600 hover:text-red-800" title="Xóa">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* ĐÁNH GIÁ NHÂN VIÊN */}
          {activeTab === 'evaluations' && (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã ĐG</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nhân viên</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kỳ đánh giá</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Người đánh giá</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Điểm kỹ năng</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Điểm tổng thể</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Xếp loại</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hoạt động</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {evaluationData.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{item.maDanhGia}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.tenNhanVien}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.kyDanhGia}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.nguoiDanhGia}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center">
                          <Star className="w-4 h-4 text-yellow-400 mr-1" />
                          <span>{item.diemKyNang}</span>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center">
                          <Award className="w-4 h-4 text-purple-400 mr-1" />
                          <span>{item.diemTongThe}</span>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.xepLoai === 'Xuất sắc' ? 'bg-green-100 text-green-800' :
                          item.xepLoai === 'Tốt' ? 'bg-blue-100 text-blue-800' :
                          item.xepLoai === 'Khá' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {item.xepLoai}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => openDetailModal(item)}
                            className="text-blue-600 hover:text-blue-800"
                            title="Xem chi tiết"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-800" title="Chỉnh sửa">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="text-red-600 hover:text-red-800" title="Xóa">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* BẢNG LƯƠNG NHÂN VIÊN */}
          {activeTab === 'salaries' && (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã NV</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên nhân viên</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Chức vụ</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tháng/Năm</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lương cơ bản</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tổng thu nhập</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thực lĩnh</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hoạt động</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {salaryData.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{item.maNhanVien}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.tenNhanVien}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.chucVu}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.thangNam}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.luongCoBan.toLocaleString('vi-VN')} VNĐ
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className="font-medium text-green-600">
                          {item.tongThuNhap.toLocaleString('vi-VN')} VNĐ
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className="font-bold text-blue-600">
                          {item.thucLinh.toLocaleString('vi-VN')} VNĐ
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => openDetailModal(item)}
                            className="text-blue-600 hover:text-blue-800"
                            title="Xem chi tiết"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-800" title="Chỉnh sửa">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="text-red-600 hover:text-red-800" title="Xóa">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Detail Modal */}
        {isDetailModalOpen && selectedItem && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-800">Chi tiết thông tin</h2>
                  <button
                    onClick={closeDetailModal}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {Object.entries(selectedItem).map(([key, value]) => (
                    <div key={key} className="bg-gray-50 p-4 rounded-lg">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {key.charAt(0).toUpperCase() + key.slice(1)}
                      </label>
                      <p className="text-sm text-gray-900">{String(value)}</p>
                    </div>
                  ))}
                </div>

                <div className="flex justify-end gap-4 mt-6">
                  <button
                    onClick={closeDetailModal}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Đóng
                  </button>
                  <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    Chỉnh sửa
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default QualityPersonnel;
