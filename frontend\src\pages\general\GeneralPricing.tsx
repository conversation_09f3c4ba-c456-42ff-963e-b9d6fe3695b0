import React, { useState } from 'react';
import {
  Calculator,
  FileText,
  ShoppingCart,
  Plus,
  Search,
  Filter,
  Download,
  Edit,
  Eye,
  Trash2
} from 'lucide-react';

const GeneralPricing = () => {
  const [activeTab, setActiveTab] = useState<'requests' | 'quotes' | 'orders'>('requests');

  // D<PERSON> liệu mẫu cho DANH SÁCH YCBG
  const requestData = [
    {
      stt: 1,
      ngayYeuCau: '2024-01-15',
      maYeuCau: 'YCBG001',
      maNhanVien: 'NV001',
      tenNhanVien: 'Nguyễn Văn A',
      maKhachHang: 'KH001',
      tenKhachHang: 'Công ty ABC',
      maSanPham: 'SP001',
      moTaSanPham: 'Khoai tây chiên',
      loaiSanPham: 'Thực phẩm đông lạnh',
      yeuCauSanPham: 'Chất lượng cao',
      dongG<PERSON>: 'Túi 500g',
      soLuong: 1000,
      donViTinh: 'Kg',
      hinhThucV<PERSON><PERSON>huyen: 'Container',
      hinh<PERSON>hu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'T/T',
      quocGia: 'Việt Nam',
      cangDen: 'Cảng Sài Gòn',
      giaDoiThu: 25000,
      giaBanGanNhat: 24000,
      ghiChu: 'Giao hàng nhanh'
    },
    {
      stt: 2,
      ngayYeuCau: '2024-01-16',
      maYeuCau: 'YCBG002',
      maNhanVien: 'NV002',
      tenNhanVien: 'Trần Thị B',
      maKhachHang: 'KH002',
      tenKhachHang: 'Công ty XYZ',
      maSanPham: 'SP002',
      moTaSanPham: 'Cà rốt sấy',
      loaiSanPham: 'Thực phẩm sấy khô',
      yeuCauSanPham: 'Organic',
      dongGoi: 'Hộp 1kg',
      soLuong: 500,
      donViTinh: 'Kg',
      hinhThucVanChuyen: 'Air freight',
      hinhThucThanhToan: 'L/C',
      quocGia: 'Nhật Bản',
      cangDen: 'Tokyo Port',
      giaDoiThu: 45000,
      giaBanGanNhat: 43000,
      ghiChu: 'Yêu cầu chứng nhận organic'
    }
  ];

	  // State for request details modal
	  type RequestItem = typeof requestData[number];
	  const [isRequestModalOpen, setIsRequestModalOpen] = useState(false);
	  const [selectedRequest, setSelectedRequest] = useState<RequestItem | null>(null);
	  const openRequestDetails = (item: RequestItem) => {
	    setSelectedRequest(item);
	    setIsRequestModalOpen(true);
	  };
	  const closeRequestDetails = () => {
	    setIsRequestModalOpen(false);
	    setSelectedRequest(null);
	  };

	  // State for order details modal
	  type OrderItem = typeof orderData[number];
	  const [isOrderModalOpen, setIsOrderModalOpen] = useState(false);
	  const [selectedOrder, setSelectedOrder] = useState<OrderItem | null>(null);
	  const openOrderDetails = (item: OrderItem) => {
	    setSelectedOrder(item);
	    setIsOrderModalOpen(true);
	  };
	  const closeOrderDetails = () => {
	    setIsOrderModalOpen(false);
	    setSelectedOrder(null);
	  };


  // Dữ liệu mẫu cho DANH SÁCH BÁO GIÁ
  const quoteData = [
    {
      stt: 1,
      ngayBaoGia: '2024-01-17',
      maBaoGia: 'BG001',
      maYeuCauBaoGia: 'YCBG001',
      giaBan: 26000,
      tinhTrang: 'Đã gửi',
    },
    {
      stt: 2,
      ngayBaoGia: '2024-01-18',
      maBaoGia: 'BG002',
      maYeuCauBaoGia: 'YCBG002',
      giaBan: 44000,
      tinhTrang: 'Chờ phản hồi',
    }
  ];

  // Dữ liệu mẫu cho DANH SÁCH ĐƠN HÀNG
  const orderData = [
    {
      stt: 1,
      ngayDatHang: '2024-01-20',
      thoiHanGiaoHang: 30,
      maDonHang: 'DH001',
      maYCBG: 'YCBG001',
      maKeHoachSanXuat: 'KHSX001',
      maKeHoachXuatHang: 'KHXH001',
      dongGoi: 'Túi 500g',
      soLuong: 1000,
      keHoach: 'Kế hoạch A',
      soGioCanSanXuat: 120,
      soGioHoanThanhThucTe: 115,
      ngayLenXe: '2024-02-15',
      trangThai: 'Đang sản xuất',
      tienDo: '85%',
      phanHoiKhachHang: 'Hài lòng',
      maKhachHang: 'KH001',
      maBaoGia: 'BG001',
      hinhThucVanChuyen: 'Container',
      giaTriDonHang: 26000000,
      thanhToanDot1USD: 13000,
      thanhToanDot1VND: 0,
      ngayThanhToan1: '2024-01-25',
      thanhToanDot2USD: 13000,
      thanhToanDot2VND: 0,
      ngayThanhToan2: '2024-02-20',
      quocGia: 'Việt Nam',
      cangDen: 'Cảng Sài Gòn',
      ngayTauDen: '2024-02-25',
      soContainer: 2,
      maNhanVienBanHang: 'NV001'
    }
  ];

  const tabs = [
    { id: 'requests', name: 'DANH SÁCH YCBG', icon: <FileText className="w-4 h-4" />, count: requestData.length },
    { id: 'quotes', name: 'DANH SÁCH BÁO GIÁ', icon: <Calculator className="w-4 h-4" />, count: quoteData.length },
    { id: 'orders', name: 'DANH SÁCH ĐƠN HÀNG', icon: <ShoppingCart className="w-4 h-4" />, count: orderData.length }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2 flex items-center">
            <Calculator className="w-8 h-8 text-blue-600 mr-3" />
            Phòng giá thành
          </h1>
          <p className="text-gray-600">Quản lý yêu cầu báo giá, báo giá và đơn hàng</p>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* Flexbox 1: Tổng quan đơn hàng */}
          <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl shadow-lg border border-blue-200 p-6 hover:shadow-xl transition-all duration-300">
            <h3 className="text-xl font-bold text-blue-800 mb-6 flex items-center">
              <div className="p-2 bg-blue-600 rounded-lg mr-3">
                <FileText className="w-5 h-5 text-white" />
              </div>
              Tổng quan đơn hàng
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-4 bg-white rounded-xl shadow-sm border-l-4 border-blue-500 hover:shadow-md transition-shadow">
                <span className="text-sm font-semibold text-gray-700">Danh sách YCBG</span>
                <span className="text-2xl font-bold text-blue-600">{requestData.length}</span>
              </div>
              <div className="flex justify-between items-center p-4 bg-white rounded-xl shadow-sm border-l-4 border-emerald-500 hover:shadow-md transition-shadow">
                <span className="text-sm font-semibold text-gray-700">Danh sách báo giá</span>
                <span className="text-2xl font-bold text-emerald-600">{quoteData.length}</span>
              </div>
              <div className="flex justify-between items-center p-4 bg-white rounded-xl shadow-sm border-l-4 border-violet-500 hover:shadow-md transition-shadow">
                <span className="text-sm font-semibold text-gray-700">Danh sách đơn hàng</span>
                <span className="text-2xl font-bold text-violet-600">{orderData.length}</span>
              </div>
            </div>
          </div>

          {/* Flexbox 2: Tổng DS báo giá */}
          <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-xl shadow-lg border border-emerald-200 p-6 hover:shadow-xl transition-all duration-300">
            <h3 className="text-xl font-bold text-emerald-800 mb-6 flex items-center">
              <div className="p-2 bg-emerald-600 rounded-lg mr-3">
                <Calculator className="w-5 h-5 text-white" />
              </div>
              Tổng DS báo giá
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-4 bg-white rounded-xl shadow-sm border-l-4 border-emerald-500 hover:shadow-md transition-shadow">
                <span className="text-sm font-semibold text-gray-700">DS báo giá đã gửi</span>
                <span className="text-2xl font-bold text-emerald-600">
                  {quoteData.filter(item => item.tinhTrang === 'Đã gửi').length}
                </span>
              </div>
              <div className="flex justify-between items-center p-4 bg-white rounded-xl shadow-sm border-l-4 border-amber-500 hover:shadow-md transition-shadow">
                <span className="text-sm font-semibold text-gray-700">DS báo giá chờ phản hồi</span>
                <span className="text-2xl font-bold text-amber-600">
                  {quoteData.filter(item => item.tinhTrang === 'Chờ phản hồi').length}
                </span>
              </div>
            </div>
          </div>

          {/* Flexbox 3: Tổng DS đơn hàng */}
          <div className="bg-gradient-to-br from-violet-50 to-violet-100 rounded-xl shadow-lg border border-violet-200 p-6 hover:shadow-xl transition-all duration-300">
            <h3 className="text-xl font-bold text-violet-800 mb-6 flex items-center">
              <div className="p-2 bg-violet-600 rounded-lg mr-3">
                <ShoppingCart className="w-5 h-5 text-white" />
              </div>
              Tổng DS đơn hàng
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-4 bg-white rounded-xl shadow-sm border-l-4 border-slate-500 hover:shadow-md transition-shadow">
                <span className="text-sm font-semibold text-gray-700">ĐH chờ sản xuất</span>
                <span className="text-2xl font-bold text-slate-600">
                  {orderData.filter(item => item.trangThai === 'Chờ sản xuất').length}
                </span>
              </div>
              <div className="flex justify-between items-center p-4 bg-white rounded-xl shadow-sm border-l-4 border-blue-500 hover:shadow-md transition-shadow">
                <span className="text-sm font-semibold text-gray-700">ĐH đang sản xuất</span>
                <span className="text-2xl font-bold text-blue-600">
                  {orderData.filter(item => item.trangThai === 'Đang sản xuất').length}
                </span>
              </div>
              <div className="flex justify-between items-center p-4 bg-white rounded-xl shadow-sm border-l-4 border-emerald-500 hover:shadow-md transition-shadow">
                <span className="text-sm font-semibold text-gray-700">ĐH đã sản xuất</span>
                <span className="text-2xl font-bold text-emerald-600">
                  {orderData.filter(item => item.trangThai === 'Hoàn thành').length}
                </span>
              </div>
              <div className="flex justify-between items-center p-4 bg-white rounded-xl shadow-sm border-l-4 border-red-500 hover:shadow-md transition-shadow">
                <span className="text-sm font-semibold text-gray-700">ĐH đã hủy</span>
                <span className="text-2xl font-bold text-red-600">
                  {orderData.filter(item => item.trangThai === 'Đã hủy').length}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.icon}
                  {tab.name}
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    activeTab === tab.id ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                  }`}>
                    {tab.count}
                  </span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Action Bar */}
        <div className="mb-6 bg-white rounded-lg shadow-sm p-4">
          <div className="flex flex-wrap gap-4 items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Tìm kiếm..."
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-64"
                />
              </div>
              <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                <Filter className="h-4 w-4" />
                Lọc
              </button>
            </div>
            <div className="flex items-center gap-2">
              <button className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                <Download className="h-4 w-4" />
                Xuất Excel
              </button>
              <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                <Plus className="h-4 w-4" />
                Thêm mới
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="bg-white rounded-lg shadow-sm">
          {/* DANH SÁCH YCBG */}
          {activeTab === 'requests' && (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">STT</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày yêu cầu báo giá</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã yêu cầu báo giá</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã nhân viên yêu cầu</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên nhân viên yêu cầu</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã khách hàng</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên khách hàng</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hoạt động</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {requestData.map((item) => (
                    <tr key={item.stt} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.stt}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.ngayYeuCau}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{item.maYeuCau}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.maNhanVien}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.tenNhanVien}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.maKhachHang}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.tenKhachHang}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => openRequestDetails(item)}
                            className="text-blue-600 hover:text-blue-800"
                            title="Xem chi tiết"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-800" title="Chỉnh sửa">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="text-red-600 hover:text-red-800" title="Xóa">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* DANH SÁCH BÁO GIÁ */}
          {activeTab === 'quotes' && (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">STT</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày báo giá</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã báo giá</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã yêu cầu báo giá</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Giá bán (ĐVT)</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tình trạng</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hoạt động</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {quoteData.map((item) => (
                    <tr key={item.stt} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.stt}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.ngayBaoGia}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{item.maBaoGia}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.maYeuCauBaoGia}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.giaBan.toLocaleString()} VNĐ</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.tinhTrang === 'Đã gửi' ? 'bg-green-100 text-green-800' :
                          item.tinhTrang === 'Chờ phản hồi' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {item.tinhTrang}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <button className="text-blue-600 hover:text-blue-800">
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-800">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="text-red-600 hover:text-red-800">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* DANH SÁCH ĐƠN HÀNG */}
          {activeTab === 'orders' && (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">STT</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày đặt hàng</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thời hạn giao hàng (ngày)</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã Đơn Hàng</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã YCBG</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã kế hoạch sản xuất</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã kế hoạch xuất hàng</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng Thái</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hoạt động</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {orderData.map((item) => (
                    <tr key={item.stt} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.stt}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.ngayDatHang}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.thoiHanGiaoHang}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{item.maDonHang}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.maYCBG}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.maKeHoachSanXuat}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.maKeHoachXuatHang}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.trangThai === 'Đang sản xuất' ? 'bg-blue-100 text-blue-800' :
                          item.trangThai === 'Hoàn thành' ? 'bg-green-100 text-green-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {item.trangThai}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => openOrderDetails(item)}
                            className="text-blue-600 hover:text-blue-800"
                            title="Xem chi tiết"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-800" title="Chỉnh sửa">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="text-red-600 hover:text-red-800" title="Xóa">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Request Details Modal */}
      {isRequestModalOpen && selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                Chi tiết yêu cầu báo giá - {selectedRequest.maYeuCau}
              </h3>
              <button
                onClick={closeRequestDetails}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Modal Body */}
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Thông tin cơ bản */}
                <div className="space-y-4">
                  <h4 className="text-md font-semibold text-gray-800 border-b pb-2">Thông tin cơ bản</h4>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">STT:</label>
                      <p className="text-sm text-gray-900">{selectedRequest.stt}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Ngày yêu cầu báo giá:</label>
                      <p className="text-sm text-gray-900">{selectedRequest.ngayYeuCau}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Mã yêu cầu báo giá:</label>
                      <p className="text-sm text-gray-900 font-medium text-blue-600">{selectedRequest.maYeuCau}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Mã nhân viên yêu cầu:</label>
                      <p className="text-sm text-gray-900">{selectedRequest.maNhanVien}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Tên nhân viên yêu cầu:</label>
                      <p className="text-sm text-gray-900">{selectedRequest.tenNhanVien}</p>
                    </div>
                  </div>
                </div>

                {/* Thông tin khách hàng */}
                <div className="space-y-4">
                  <h4 className="text-md font-semibold text-gray-800 border-b pb-2">Thông tin khách hàng</h4>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Mã khách hàng:</label>
                      <p className="text-sm text-gray-900">{selectedRequest.maKhachHang}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Tên khách hàng:</label>
                      <p className="text-sm text-gray-900">{selectedRequest.tenKhachHang}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Quốc gia:</label>
                      <p className="text-sm text-gray-900">{selectedRequest.quocGia}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Cảng đến:</label>
                      <p className="text-sm text-gray-900">{selectedRequest.cangDen}</p>
                    </div>
                  </div>
                </div>

                {/* Thông tin sản phẩm */}
                <div className="space-y-4">
                  <h4 className="text-md font-semibold text-gray-800 border-b pb-2">Thông tin sản phẩm</h4>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Mã sản phẩm:</label>
                      <p className="text-sm text-gray-900">{selectedRequest.maSanPham}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Mô tả sản phẩm:</label>
                      <p className="text-sm text-gray-900">{selectedRequest.moTaSanPham}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Loại sản phẩm:</label>
                      <p className="text-sm text-gray-900">{selectedRequest.loaiSanPham}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Yêu cầu sản phẩm:</label>
                      <p className="text-sm text-gray-900">{selectedRequest.yeuCauSanPham}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Đóng gói:</label>
                      <p className="text-sm text-gray-900">{selectedRequest.dongGoi}</p>
                    </div>
                  </div>
                </div>

                {/* Thông tin đơn hàng */}
                <div className="space-y-4">
                  <h4 className="text-md font-semibold text-gray-800 border-b pb-2">Thông tin đơn hàng</h4>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Số lượng:</label>
                      <p className="text-sm text-gray-900">{selectedRequest.soLuong.toLocaleString()}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Đơn vị tính:</label>
                      <p className="text-sm text-gray-900">{selectedRequest.donViTinh}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Hình thức vận chuyển:</label>
                      <p className="text-sm text-gray-900">{selectedRequest.hinhThucVanChuyen}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Hình thức thanh toán:</label>
                      <p className="text-sm text-gray-900">{selectedRequest.hinhThucThanhToan}</p>
                    </div>
                  </div>
                </div>

                {/* Thông tin giá */}
                <div className="space-y-4 md:col-span-2">
                  <h4 className="text-md font-semibold text-gray-800 border-b pb-2">Thông tin giá</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Giá đối thủ bán:</label>
                      <p className="text-sm text-gray-900 font-medium">{selectedRequest.giaDoiThu.toLocaleString()} VNĐ</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Giá bán gần nhất:</label>
                      <p className="text-sm text-gray-900 font-medium">{selectedRequest.giaBanGanNhat.toLocaleString()} VNĐ</p>
                    </div>
                  </div>
                </div>

                {/* Ghi chú */}
                <div className="space-y-4 md:col-span-2">
                  <h4 className="text-md font-semibold text-gray-800 border-b pb-2">Ghi chú</h4>
                  <div>
                    <p className="text-sm text-gray-900">{selectedRequest.ghiChu}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
              <button
                onClick={closeRequestDetails}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Đóng
              </button>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                Chỉnh sửa
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Order Details Modal */}
      {isOrderModalOpen && selectedOrder && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                Chi tiết đơn hàng - {selectedOrder.maDonHang}
              </h3>
              <button
                onClick={closeOrderDetails}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Modal Body */}
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Thông tin cơ bản */}
                <div className="space-y-4">
                  <h4 className="text-md font-semibold text-gray-800 border-b pb-2">Thông tin cơ bản</h4>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">STT:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.stt}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Ngày đặt hàng:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.ngayDatHang}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Thời hạn giao hàng:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.thoiHanGiaoHang} ngày</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Mã đơn hàng:</label>
                      <p className="text-sm text-gray-900 font-medium text-blue-600">{selectedOrder.maDonHang}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Mã YCBG:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.maYCBG}</p>
                    </div>
                  </div>
                </div>

                {/* Kế hoạch sản xuất */}
                <div className="space-y-4">
                  <h4 className="text-md font-semibold text-gray-800 border-b pb-2">Kế hoạch sản xuất</h4>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Mã kế hoạch sản xuất:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.maKeHoachSanXuat}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Mã kế hoạch xuất hàng:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.maKeHoachXuatHang}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Kế hoạch:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.keHoach}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Số giờ cần sản xuất:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.soGioCanSanXuat} giờ</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Số giờ hoàn thành thực tế:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.soGioHoanThanhThucTe} giờ</p>
                    </div>
                  </div>
                </div>

                {/* Thông tin sản phẩm */}
                <div className="space-y-4">
                  <h4 className="text-md font-semibold text-gray-800 border-b pb-2">Thông tin sản phẩm</h4>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Đóng gói:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.dongGoi}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Số lượng:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.soLuong.toLocaleString()} Kg</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Ngày lên xe:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.ngayLenXe}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Trạng thái:</label>
                      <p className="text-sm">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          selectedOrder.trangThai === 'Đang sản xuất' ? 'bg-blue-100 text-blue-800' :
                          selectedOrder.trangThai === 'Hoàn thành' ? 'bg-green-100 text-green-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {selectedOrder.trangThai}
                        </span>
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Tiến độ:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.tienDo}</p>
                    </div>
                  </div>
                </div>

                {/* Thông tin khách hàng */}
                <div className="space-y-4">
                  <h4 className="text-md font-semibold text-gray-800 border-b pb-2">Thông tin khách hàng</h4>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Mã khách hàng:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.maKhachHang}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Mã báo giá:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.maBaoGia}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Phản hồi từ khách hàng:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.phanHoiKhachHang}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Mã nhân viên bán hàng:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.maNhanVienBanHang}</p>
                    </div>
                  </div>
                </div>

                {/* Vận chuyển */}
                <div className="space-y-4">
                  <h4 className="text-md font-semibold text-gray-800 border-b pb-2">Thông tin vận chuyển</h4>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Hình thức vận chuyển:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.hinhThucVanChuyen}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Quốc gia:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.quocGia}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Cảng đến:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.cangDen}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Ngày tàu đến:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.ngayTauDen}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Số container:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.soContainer}</p>
                    </div>
                  </div>
                </div>

                {/* Thông tin thanh toán */}
                <div className="space-y-4">
                  <h4 className="text-md font-semibold text-gray-800 border-b pb-2">Thông tin thanh toán</h4>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Giá trị đơn hàng:</label>
                      <p className="text-sm text-gray-900 font-medium">{selectedOrder.giaTriDonHang.toLocaleString()} VNĐ</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Thanh toán đợt 1 (USD):</label>
                      <p className="text-sm text-gray-900">{selectedOrder.thanhToanDot1USD.toLocaleString()} USD</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Thanh toán đợt 1 (VNĐ):</label>
                      <p className="text-sm text-gray-900">{selectedOrder.thanhToanDot1VND.toLocaleString()} VNĐ</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Ngày thanh toán 1:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.ngayThanhToan1}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Thanh toán đợt 2 (USD):</label>
                      <p className="text-sm text-gray-900">{selectedOrder.thanhToanDot2USD.toLocaleString()} USD</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Thanh toán đợt 2 (VNĐ):</label>
                      <p className="text-sm text-gray-900">{selectedOrder.thanhToanDot2VND.toLocaleString()} VNĐ</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Ngày thanh toán 2:</label>
                      <p className="text-sm text-gray-900">{selectedOrder.ngayThanhToan2}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
              <button
                onClick={closeOrderDetails}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Đóng
              </button>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                Chỉnh sửa
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GeneralPricing;
