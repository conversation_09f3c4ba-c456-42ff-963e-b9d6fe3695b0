import React, { useState } from 'react';
import {
  Settings,
  FileText,
  CheckCircle,
  Plus,
  Search,
  Filter,
  Download,
  Edit,
  Eye,
  Trash2,
  Target
} from 'lucide-react';

const QualityProcess = () => {
  const [activeTab, setActiveTab] = useState<'standards' | 'procedures' | 'inspections' | 'improvements'>('standards');

  // Dữ liệu mẫu cho TIÊU CHUẨN CHẤT LƯỢNG
  const standardData = [
    {
      id: 1,
      maTieuChuan: 'TC001',
      tenTieuChuan: 'Tiêu chuẩn HACCP cho sản xuất thực phẩm',
      loaiTieuChuan: 'An toàn thực phẩm',
      phienBan: 'v2.1',
      ngayBanHanh: '2024-01-15',
      ngayHieuLuc: '2024-02-01',
      nguoiPhuTrach: 'Trần Thị Bình',
      phongBanApDung: 'Sản xuất, QC',
      trangThai: 'Đang áp dụng',
      mucDo<PERSON>u<PERSON><PERSON>: 'Cao',
      moTa: '<PERSON><PERSON> thống phân tích mối nguy và điểm kiểm soát tới hạn',
      yeuCauTuanThu: 'Bắt buộc cho tất cả sản phẩm thực phẩm',
      taiLieuLienQuan: 'ISO 22000, FDA Guidelines',
      ghiChu: 'Cập nhật theo quy định mới nhất'
    },
    {
      id: 2,
      maTieuChuan: 'TC002',
      tenTieuChuan: 'Tiêu chuẩn ISO 9001:2015',
      loaiTieuChuan: 'Hệ thống quản lý chất lượng',
      phienBan: 'v1.0',
      ngayBanHanh: '2023-12-01',
      ngayHieuLuc: '2024-01-01',
      nguoiPhuTrach: 'Lê Văn Cường',
      phongBanApDung: 'Toàn công ty',
      trangThai: 'Đang áp dụng',
      mucDoUuTien: 'Cao',
      moTa: 'Hệ thống quản lý chất lượng theo tiêu chuẩn quốc tế',
      yeuCauTuanThu: 'Áp dụng cho tất cả quy trình',
      taiLieuLienQuan: 'ISO 9001:2015 Standard',
      ghiChu: 'Đánh giá định kỳ 6 tháng/lần'
    }
  ];

  // Dữ liệu mẫu cho QUY TRÌNH KIỂM SOÁT
  const procedureData = [
    {
      id: 1,
      maQuyTrinh: 'QT001',
      tenQuyTrinh: 'Quy trình kiểm tra nguyên liệu đầu vào',
      loaiQuyTrinh: 'Kiểm tra chất lượng',
      buocThucHien: '5 bước',
      thoiGianThucHien: '30 phút',
      nguoiThucHien: 'Nhân viên QC',
      tanSuatKiemTra: 'Mỗi lô hàng',
      trangThai: 'Đang áp dụng',
      mucDoRuiRo: 'Cao',
      ngayTao: '2024-01-10',
      nguoiTao: 'Trần Thị Bình',
      lanCapNhatCuoi: '2024-03-15',
      moTa: 'Kiểm tra chất lượng, xuất xứ và tính an toàn của nguyên liệu',
      tieuChiDanhGia: 'Màu sắc, mùi vị, độ ẩm, tạp chất',
      ghiChu: 'Cần cập nhật theo tiêu chuẩn mới'
    },
    {
      id: 2,
      maQuyTrinh: 'QT002',
      tenQuyTrinh: 'Quy trình kiểm soát nhiệt độ sản xuất',
      loaiQuyTrinh: 'Kiểm soát quy trình',
      buocThucHien: '3 bước',
      thoiGianThucHien: '15 phút',
      nguoiThucHien: 'Nhân viên sản xuất',
      tanSuatKiemTra: 'Mỗi 2 giờ',
      trangThai: 'Đang áp dụng',
      mucDoRuiRo: 'Trung bình',
      ngayTao: '2024-02-01',
      nguoiTao: 'Nguyễn Văn An',
      lanCapNhatCuoi: '2024-03-20',
      moTa: 'Giám sát và kiểm soát nhiệt độ trong quá trình sản xuất',
      tieuChiDanhGia: 'Nhiệt độ trong khoảng 65-75°C',
      ghiChu: 'Thiết bị đo cần hiệu chuẩn định kỳ'
    }
  ];

  // Dữ liệu mẫu cho KIỂM TRA CHẤT LƯỢNG
  const inspectionData = [
    {
      id: 1,
      maKiemTra: 'KT001',
      tenKiemTra: 'Kiểm tra chất lượng sản phẩm hoàn thành',
      loaiKiemTra: 'Kiểm tra cuối cùng',
      sanPham: 'Khoai tây chiên đông lạnh',
      loBatch: 'LOT240315001',
      ngayKiemTra: '2024-03-15',
      nguoiKiemTra: 'Nguyễn Văn An',
      ketQua: 'Đạt',
      diemChatLuong: 9.2,
      trangThai: 'Hoàn thành',
      mucDoUuTien: 'Cao',
      tieuChiKiemTra: 'Màu sắc, độ giòn, hàm lượng dầu',
      ketQuaChiTiet: 'Màu vàng đều, độ giòn tốt, dầu <3%',
      khuyenCao: 'Sản phẩm đạt tiêu chuẩn xuất khẩu',
      ghiChu: 'Lô hàng chất lượng cao'
    },
    {
      id: 2,
      maKiemTra: 'KT002',
      tenKiemTra: 'Kiểm tra vi sinh vật',
      loaiKiemTra: 'Kiểm tra an toàn',
      sanPham: 'Cà rốt baby đóng gói',
      loBatch: 'LOT240316002',
      ngayKiemTra: '2024-03-16',
      nguoiKiemTra: 'Trần Thị Bình',
      ketQua: 'Đạt',
      diemChatLuong: 9.5,
      trangThai: 'Hoàn thành',
      mucDoUuTien: 'Cao',
      tieuChiKiemTra: 'E.coli, Salmonella, tổng vi sinh',
      ketQuaChiTiet: 'Không phát hiện vi sinh vật có hại',
      khuyenCao: 'Duy trì quy trình vệ sinh hiện tại',
      ghiChu: 'Kết quả tốt, an toàn cho người tiêu dùng'
    }
  ];

  // Dữ liệu mẫu cho CẢI TIẾN QUY TRÌNH
  const improvementData = [
    {
      id: 1,
      maDeXuat: 'DX001',
      tenDeXuat: 'Cải tiến quy trình đóng gói tự động',
      loaiCaiTien: 'Tự động hóa',
      nguoiDeXuat: 'Lê Văn Cường',
      phongBan: 'Sản xuất',
      ngayDeXuat: '2024-03-01',
      trangThai: 'Đang triển khai',
      mucDoUuTien: 'Cao',
      ngayDuKienHoanThanh: '2024-06-30',
      chiPhiUocTinh: 500000000,
      laiIchMongDoi: 'Tăng 30% năng suất, giảm 50% lỗi đóng gói',
      moTaVanDe: 'Quy trình đóng gói thủ công gây chậm trễ và sai sót',
      giaiPhapDeXuat: 'Lắp đặt dây chuyền đóng gói tự động',
      tienDoThucHien: '60%',
      ghiChu: 'Đang chờ phê duyệt ngân sách bổ sung'
    },
    {
      id: 2,
      maDeXuat: 'DX002',
      tenDeXuat: 'Cải tiến hệ thống theo dõi nhiệt độ',
      loaiCaiTien: 'Công nghệ',
      nguoiDeXuat: 'Nguyễn Văn An',
      phongBan: 'Chất lượng',
      ngayDeXuat: '2024-02-15',
      trangThai: 'Hoàn thành',
      mucDoUuTien: 'Trung bình',
      ngayDuKienHoanThanh: '2024-04-15',
      chiPhiUocTinh: 150000000,
      laiIchMongDoi: 'Giảm 80% thời gian kiểm tra nhiệt độ',
      moTaVanDe: 'Kiểm tra nhiệt độ thủ công mất thời gian và không chính xác',
      giaiPhapDeXuat: 'Lắp đặt cảm biến nhiệt độ tự động với cảnh báo',
      tienDoThucHien: '100%',
      ghiChu: 'Đã triển khai thành công, hiệu quả cao'
    }
  ];

  // State for modals
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);

  const openDetailModal = (item: any) => {
    setSelectedItem(item);
    setIsDetailModalOpen(true);
  };

  const closeDetailModal = () => {
    setIsDetailModalOpen(false);
    setSelectedItem(null);
  };

  const tabs = [
    { id: 'standards', name: 'TIÊU CHUẨN CHẤT LƯỢNG', icon: <Settings className="w-4 h-4" />, count: standardData.length },
    { id: 'procedures', name: 'QUY TRÌNH KIỂM SOÁT', icon: <FileText className="w-4 h-4" />, count: procedureData.length },
    { id: 'inspections', name: 'KIỂM TRA CHẤT LƯỢNG', icon: <CheckCircle className="w-4 h-4" />, count: inspectionData.length },
    { id: 'improvements', name: 'CẢI TIẾN QUY TRÌNH', icon: <Target className="w-4 h-4" />, count: improvementData.length }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2 flex items-center">
            <Settings className="w-8 h-8 text-blue-600 mr-3" />
            Quản lý quy trình chất lượng
          </h1>
          <p className="text-gray-600">Quản lý tiêu chuẩn, quy trình, kiểm tra và cải tiến chất lượng</p>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
          {/* Tổng quan tiêu chuẩn */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <Settings className="w-5 h-5 text-blue-600 mr-2" />
              Tổng quan tiêu chuẩn
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Tổng tiêu chuẩn</span>
                <span className="text-lg font-bold text-blue-600">{standardData.length}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Đang áp dụng</span>
                <span className="text-lg font-bold text-green-600">
                  {standardData.filter(item => item.trangThai === 'Đang áp dụng').length}
                </span>
              </div>
            </div>
          </div>

          {/* Tổng quan quy trình */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <FileText className="w-5 h-5 text-green-600 mr-2" />
              Tổng quan quy trình
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Tổng quy trình</span>
                <span className="text-lg font-bold text-green-600">{procedureData.length}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Rủi ro cao</span>
                <span className="text-lg font-bold text-red-600">
                  {procedureData.filter(item => item.mucDoRuiRo === 'Cao').length}
                </span>
              </div>
            </div>
          </div>

          {/* Tổng quan kiểm tra */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <CheckCircle className="w-5 h-5 text-purple-600 mr-2" />
              Tổng quan kiểm tra
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Đã kiểm tra</span>
                <span className="text-lg font-bold text-purple-600">{inspectionData.length}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Kết quả đạt</span>
                <span className="text-lg font-bold text-green-600">
                  {inspectionData.filter(item => item.ketQua === 'Đạt').length}
                </span>
              </div>
            </div>
          </div>

          {/* Tổng quan cải tiến */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <Target className="w-5 h-5 text-orange-600 mr-2" />
              Tổng quan cải tiến
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Đề xuất cải tiến</span>
                <span className="text-lg font-bold text-orange-600">{improvementData.length}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Hoàn thành</span>
                <span className="text-lg font-bold text-green-600">
                  {improvementData.filter(item => item.trangThai === 'Hoàn thành').length}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.icon}
                  {tab.name}
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    activeTab === tab.id ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                  }`}>
                    {tab.count}
                  </span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Action Bar */}
        <div className="mb-6 bg-white rounded-lg shadow-sm p-4">
          <div className="flex flex-wrap gap-4 items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Tìm kiếm quy trình..."
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-64"
                />
              </div>
              <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                <Filter className="h-4 w-4" />
                Lọc
              </button>
            </div>
            <div className="flex items-center gap-2">
              <button className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                <Download className="h-4 w-4" />
                Xuất Excel
              </button>
              <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                <Plus className="h-4 w-4" />
                Thêm mới
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="bg-white rounded-lg shadow-sm">
          {/* TIÊU CHUẨN CHẤT LƯỢNG */}
          {activeTab === 'standards' && (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã TC</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên tiêu chuẩn</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loại</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phiên bản</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày hiệu lực</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Người phụ trách</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hoạt động</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {standardData.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{item.maTieuChuan}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.tenTieuChuan}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.loaiTieuChuan === 'An toàn thực phẩm' ? 'bg-red-100 text-red-800' :
                          item.loaiTieuChuan === 'Hệ thống quản lý chất lượng' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {item.loaiTieuChuan}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.phienBan}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.ngayHieuLuc}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.nguoiPhuTrach}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.trangThai === 'Đang áp dụng' ? 'bg-green-100 text-green-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {item.trangThai}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => openDetailModal(item)}
                            className="text-blue-600 hover:text-blue-800"
                            title="Xem chi tiết"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-800" title="Chỉnh sửa">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="text-red-600 hover:text-red-800" title="Xóa">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* QUY TRÌNH KIỂM SOÁT */}
          {activeTab === 'procedures' && (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã QT</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên quy trình</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loại</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thời gian</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Người thực hiện</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tần suất</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rủi ro</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hoạt động</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {procedureData.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{item.maQuyTrinh}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.tenQuyTrinh}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.loaiQuyTrinh === 'Kiểm tra chất lượng' ? 'bg-blue-100 text-blue-800' :
                          item.loaiQuyTrinh === 'Kiểm soát quy trình' ? 'bg-green-100 text-green-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {item.loaiQuyTrinh}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.thoiGianThucHien}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.nguoiThucHien}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.tanSuatKiemTra}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.mucDoRuiRo === 'Cao' ? 'bg-red-100 text-red-800' :
                          item.mucDoRuiRo === 'Trung bình' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {item.mucDoRuiRo}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => openDetailModal(item)}
                            className="text-blue-600 hover:text-blue-800"
                            title="Xem chi tiết"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-800" title="Chỉnh sửa">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="text-red-600 hover:text-red-800" title="Xóa">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* KIỂM TRA CHẤT LƯỢNG */}
          {activeTab === 'inspections' && (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã KT</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên kiểm tra</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sản phẩm</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lô/Batch</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày kiểm tra</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Người kiểm tra</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kết quả</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Điểm CL</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hoạt động</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {inspectionData.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{item.maKiemTra}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.tenKiemTra}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.sanPham}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">{item.loBatch}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.ngayKiemTra}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.nguoiKiemTra}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.ketQua === 'Đạt' ? 'bg-green-100 text-green-800' :
                          item.ketQua === 'Không đạt' ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {item.ketQua}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center">
                          <CheckCircle className="w-4 h-4 text-green-400 mr-1" />
                          <span className="font-medium">{item.diemChatLuong}</span>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => openDetailModal(item)}
                            className="text-blue-600 hover:text-blue-800"
                            title="Xem chi tiết"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-800" title="Chỉnh sửa">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="text-red-600 hover:text-red-800" title="Xóa">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* CẢI TIẾN QUY TRÌNH */}
          {activeTab === 'improvements' && (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã ĐX</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên đề xuất</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loại cải tiến</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Người đề xuất</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày đề xuất</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tiến độ</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hoạt động</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {improvementData.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{item.maDeXuat}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.tenDeXuat}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.loaiCaiTien === 'Tự động hóa' ? 'bg-blue-100 text-blue-800' :
                          item.loaiCaiTien === 'Công nghệ' ? 'bg-purple-100 text-purple-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {item.loaiCaiTien}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.nguoiDeXuat}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.ngayDeXuat}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.trangThai === 'Hoàn thành' ? 'bg-green-100 text-green-800' :
                          item.trangThai === 'Đang triển khai' ? 'bg-blue-100 text-blue-800' :
                          item.trangThai === 'Chờ phê duyệt' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {item.trangThai}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center">
                          <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full"
                              style={{ width: `${item.tienDoThucHien}` }}
                            ></div>
                          </div>
                          <span className="text-xs font-medium">{item.tienDoThucHien}</span>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => openDetailModal(item)}
                            className="text-blue-600 hover:text-blue-800"
                            title="Xem chi tiết"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-800" title="Chỉnh sửa">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="text-red-600 hover:text-red-800" title="Xóa">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Detail Modal */}
        {isDetailModalOpen && selectedItem && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-800">Chi tiết thông tin</h2>
                  <button
                    onClick={closeDetailModal}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {Object.entries(selectedItem).map(([key, value]) => (
                    <div key={key} className="bg-gray-50 p-4 rounded-lg">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {key.charAt(0).toUpperCase() + key.slice(1)}
                      </label>
                      <p className="text-sm text-gray-900">{String(value)}</p>
                    </div>
                  ))}
                </div>

                <div className="flex justify-end gap-4 mt-6">
                  <button
                    onClick={closeDetailModal}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Đóng
                  </button>
                  <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    Chỉnh sửa
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default QualityProcess;
